FROM golang:1.22-alpine AS builder

WORKDIR /app

# Copy go mod and sum files
COPY app/go.mod app/go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY app/ ./

# Ensure dependencies are up-to-date (adds any missing entries to go.sum)
RUN go mod tidy

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o main .

# Create a minimal production image
FROM alpine:latest

WORKDIR /app

# Install ca-certificates and curl for health checks
RUN apk --no-cache add ca-certificates curl

# Copy the binary file from the builder stage
COPY --from=builder /app/main .

# Expose the application port
EXPOSE 8080

# Run the application
CMD ["./main"]
