package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

type SlackNotifier struct {
	WebhookURL string
	Enabled    bool
	Timeout    time.Duration
}

func NewSlackNotifier(webhookURL string, enabled bool) *SlackNotifier {
	return &SlackNotifier{
		WebhookURL: webhookURL,
		Enabled:    enabled,
		Timeout:    2 * time.Second,
	}
}

// SendHealAlert posts an alert to Slack including before/after states for RCA.
func (s *SlackNotifier) SendHealAlert(ctx context.Context, facilityCode, whSKU, typesenseURL string, updatedCount int, beforeFlag bool, beforeQty int, afterFlag bool, afterQty int) error {
	if s == nil || !s.Enabled || s.WebhookURL == "" {
		return nil
	}

	// Text fallback for clients without blocks support
	text := fmt.Sprintf(
		":ambulance: Typesense heal applied\nfacility=%s | wh_sku=%s | updated_docs=%d | host=%s\nbefore: is_available=%t, available_qty=%d\nafter:  is_available=%t, available_qty=%d",
		facilityCode, whSKU, updatedCount, typesenseURL, beforeFlag, beforeQty, afterFlag, afterQty,
	)

	// Block Kit payload for better readability
	payload := map[string]interface{}{
		"text": text,
		"blocks": []interface{}{
			map[string]interface{}{
				"type": "header",
				"text": map[string]interface{}{
					"type":  "plain_text",
					"text":  "Typesense Heal Applied",
					"emoji": true,
				},
			},
			map[string]interface{}{
				"type": "section",
				"fields": []interface{}{
					map[string]interface{}{"type": "mrkdwn", "text": fmt.Sprintf("*Facility*\n%s", facilityCode)},
					map[string]interface{}{"type": "mrkdwn", "text": fmt.Sprintf("*WH SKU*\n`%s`", whSKU)},
					map[string]interface{}{"type": "mrkdwn", "text": fmt.Sprintf("*Updated Docs*\n%d", updatedCount)},
					map[string]interface{}{"type": "mrkdwn", "text": fmt.Sprintf("*Typesense Host*\n<%s>", typesenseURL)},
				},
			},
			map[string]interface{}{
				"type": "section",
				"fields": []interface{}{
					map[string]interface{}{"type": "mrkdwn", "text": fmt.Sprintf("*Before*\n`is_available=%t`\n`available_qty=%d`", beforeFlag, beforeQty)},
					map[string]interface{}{"type": "mrkdwn", "text": fmt.Sprintf("*After*\n`is_available=%t`\n`available_qty=%d`", afterFlag, afterQty)},
				},
			},
			map[string]interface{}{
				"type": "context",
				"elements": []interface{}{
					map[string]interface{}{"type": "mrkdwn", "text": ":ambulance: Heal triggered by IMS variant calculation (auto)"},
				},
			},
		},
	}

	body, _ := json.Marshal(payload)

	req, _ := http.NewRequestWithContext(ctx, http.MethodPost, s.WebhookURL, bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: s.Timeout}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return fmt.Errorf("slack webhook failed: %s", resp.Status)
	}

	return nil
}
