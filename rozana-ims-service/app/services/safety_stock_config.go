package services

import (
	"context"
	"log"
	"strconv"

	"github.com/go-redis/redis/v8"
)

// SafetyStockConfigManager manages category-level safety stock configuration from Redis
// Priority order (highest to lowest):
// 1. sub_sub_category (safetystock:<sub_sub_category>)
// 2. sub_category (safetystock:<sub_category>)
// 3. category (safetystock:<category>)
// 4. Environment default (fallback)
type SafetyStockConfigManager struct {
	redis           *redis.Client
	defaultQuantity float64
}

// NewSafetyStockConfigManager creates a new SafetyStockConfigManager instance
func NewSafetyStockConfigManager(client *redis.Client, defaultQuantity float64) *SafetyStockConfigManager {
	return &SafetyStockConfigManager{
		redis:           client,
		defaultQuantity: defaultQuantity,
	}
}

// Categories represents the category hierarchy
type Categories struct {
	Category       string
	SubCategory    string
	SubSubCategory string
}

// GetSafetyStock resolves safety stock based on category hierarchy with priority
// Returns safety stock value based on priority order
func (m *SafetyStockConfigManager) GetSafetyStock(ctx context.Context, categories Categories) float64 {

	// Priority 1: Check sub_sub_category
	if categories.SubSubCategory != "" {
		if safetyStock := m.getSafetyStockFromRedis(ctx, categories.SubSubCategory); safetyStock != nil {
			log.Printf("SAFETY_STOCK_CONFIG: Using sub_sub_category config - sub_sub_category='%s', safety_stock=%v", categories.SubSubCategory, *safetyStock)
			return *safetyStock
		}
	}

	// Priority 2: Check sub_category
	if categories.SubCategory != "" {
		if safetyStock := m.getSafetyStockFromRedis(ctx, categories.SubCategory); safetyStock != nil {
			log.Printf("SAFETY_STOCK_CONFIG: Using sub_category config - sub_category='%s', safety_stock=%v", categories.SubCategory, *safetyStock)
			return *safetyStock
		}
	}

	// Priority 3: Check category
	if categories.Category != "" {
		if safetyStock := m.getSafetyStockFromRedis(ctx, categories.Category); safetyStock != nil {
			log.Printf("SAFETY_STOCK_CONFIG: Using category config - category='%s', safety_stock=%v", categories.Category, *safetyStock)
			return *safetyStock
		}
	}

	// Priority 4: Use environment default
	log.Printf("SAFETY_STOCK_CONFIG: No category-level config found, using environment default - safety_stock=%v", m.defaultQuantity)
	return m.defaultQuantity
}

// getSafetyStockFromRedis retrieves safety stock value from Redis for a given category
// Returns pointer to float64 if found, nil otherwise
func (m *SafetyStockConfigManager) getSafetyStockFromRedis(ctx context.Context, categoryValue string) *float64 {
	key := "safetystock:" + categoryValue
	val, err := m.redis.Get(ctx, key).Result()
	if err == redis.Nil {
		// Key not found
		return nil
	}
	if err != nil {
		// Error occurred
		log.Printf("SAFETY_STOCK_CONFIG: Error retrieving safety stock from Redis for key '%s': %v", key, err)
		return nil
	}

	// Parse the value as float64
	safetyStock, err := strconv.ParseFloat(val, 64)
	if err != nil {
		log.Printf("SAFETY_STOCK_CONFIG: Error parsing safety stock value '%s' for key '%s': %v", val, key, err)
		return nil
	}

	return &safetyStock
}
