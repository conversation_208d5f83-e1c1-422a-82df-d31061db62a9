package config

import (
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"
)

// InitRedis initializes a Redis client connection
func InitRedis(cfg *Config) *redis.Client {
	// Parse Redis URL and create client options
	options, err := redis.ParseURL(cfg.RedisURL)
	if err != nil {
		fmt.Printf("Error parsing Redis URL: %v\n", err)
		// Fallback to default if URL is invalid
		options = &redis.Options{
			Addr: "localhost:6379",
		}
	}

	client := redis.NewClient(options)

	// Verify connection
	ctx := context.Background()
	_, err = client.Ping(ctx).Result()
	if err != nil {
		fmt.Printf("Failed to connect to Redis: %v\n", err)
		// We're not exiting - connection may recover later
	} else {
		fmt.Println("Successfully connected to Redis", cfg.RedisURL)
	}

	return client
}
