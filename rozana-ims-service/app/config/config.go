package config

import (
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// Config holds all configuration settings
type Config struct {
	ServerPort    string
	RedisURL      string
	SentryDSN     string
	SentryEnv     string
	SentryEnabled bool
	Environment   string

	// Stock settings
	SafetyQuantity float64

	// Typesense settings
	TypesenseURL         string
	TypesenseAPIKey      string
	TypesenseCollection  string
	TypesenseTimeoutMS   string
	TypesenseHealEnabled bool

	// Slack alert settings
	SlackWebhookURL string
	SlackEnabled    bool
}

// LoadConfig loads environment variables and returns a Config struct
func LoadConfig() (*Config, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	// Set default values
	config := &Config{
		ServerPort:     getEnv("SERVER_PORT", "8080"),
		RedisURL:       getEnv("REDIS_URL", "redis://localhost:6379"),
		SentryDSN:      getEnv("SENTRY_DSN", ""),
		SentryEnv:      getEnv("SENTRY_ENV", "development"),
		SentryEnabled:  getEnv("SENTRY_ENABLED", "false") == "true",
		Environment:    getEnv("ENVIRONMENT", "development"),
		SafetyQuantity: parseFloat64(getEnv("SAFETY_QUANTITY", "0")),

		TypesenseURL:         getEnv("TYPESENSE_URL", "http://localhost:8108"),
		TypesenseAPIKey:      getEnv("TYPESENSE_API_KEY", ""),
		TypesenseCollection:  getEnv("TYPESENSE_COLLECTION", "facility_products"),
		TypesenseTimeoutMS:   getEnv("TYPESENSE_TIMEOUT_MS", "3000"),
		TypesenseHealEnabled: getEnv("TYPESENSE_HEAL_ENABLED", "false") == "true",

		SlackWebhookURL: getEnv("SLACK_WEBHOOK_URL", ""),
		SlackEnabled:    getEnv("SLACK_ENABLED", "false") == "true",
	}

	return config, nil
}

// getEnv retrieves an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

// parseFloat64 converts string to float64, returns 0 if parsing fails
func parseFloat64(s string) float64 {
	f, _ := strconv.ParseFloat(s, 64)
	return f
}
