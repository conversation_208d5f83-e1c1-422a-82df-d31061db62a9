package config

import (
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/getsentry/sentry-go"
	sentryfiber "github.com/getsentry/sentry-go/fiber"
	"github.com/gofiber/fiber/v2"
)

var sentryEnabled bool

// InitSentry initializes Sentry SDK with flag-based configuration
func InitSentry() error {
	// Check if Sentry is enabled via environment flag
	sentryEnabledStr := getEnv("SENTRY_ENABLED", "false")
	sentryEnabled = strings.ToLower(sentryEnabledStr) == "true"

	if !sentryEnabled {
		log.Println("Sentry monitoring is disabled")
		return nil
	}

	// Get Sentry DSN from environment
	sentryDSN := os.Getenv("SENTRY_DSN")
	if sentryDSN == "" {
		log.Println("SENTRY_ENABLED is true but SENTRY_DSN is not configured")
		return nil
	}

	// Get environment and release info
	environment := getEnv("ENVIRONMENT", "development")
	release := getEnv("SENTRY_RELEASE", "rozana-ims-service@1.0.0")

	// Parse sample rates
	tracesSampleRate, _ := strconv.ParseFloat(getEnv("SENTRY_TRACES_SAMPLE_RATE", "0.1"), 64)

	// Configure Sentry
	err := sentry.Init(sentry.ClientOptions{
		Dsn:              sentryDSN,
		Environment:      environment,
		Release:          release,
		TracesSampleRate: tracesSampleRate,
		// Performance monitoring
		EnableTracing: true,
		// Send default PII (personally identifiable information)
		SendDefaultPII: false,
		// Attach stack traces to all messages
		AttachStacktrace: true,
		// Sample rate for error events
		SampleRate: 1.0,
		// Maximum breadcrumbs
		MaxBreadcrumbs: 50,
		// Before send hook to filter sensitive data
		BeforeSend: beforeSendFilter,
	})

	if err != nil {
		log.Printf("Sentry initialization failed: %v", err)
		return err
	}

	log.Printf("Sentry initialized successfully for environment: %s", environment)

	// Send a test event to verify connectivity
	TestSentryConnection()

	return nil
}

// FlushSentry flushes any pending Sentry events
func FlushSentry() {
	if sentryEnabled {
		sentry.Flush(2 * time.Second)
	}
}

// GetSentryMiddleware returns Sentry Fiber middleware if enabled
func GetSentryMiddleware() fiber.Handler {
	if sentryEnabled {
		return sentryfiber.New(sentryfiber.Options{
			Repanic:         true,
			WaitForDelivery: true,
		})
	}
	// Return a no-op middleware if Sentry is disabled
	return func(c *fiber.Ctx) error {
		return c.Next()
	}
}

// beforeSendFilter filters sensitive data before sending to Sentry
func beforeSendFilter(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
	// Filter sensitive headers
	if event.Request != nil && event.Request.Headers != nil {
		sensitiveHeaders := []string{"authorization", "cookie", "x-api-key", "x-auth-token"}
		for _, header := range sensitiveHeaders {
			if _, exists := event.Request.Headers[header]; exists {
				event.Request.Headers[header] = "[Filtered]"
			}
		}
	}

	// Filter sensitive form data
	if event.Request != nil && event.Request.Data != "" {
		sensitiveFields := []string{"password", "token", "secret", "key", "auth"}
		// In Go Sentry SDK, Request.Data is a string, so we check if it contains sensitive keywords
		data := event.Request.Data
		for _, sensitiveField := range sensitiveFields {
			if strings.Contains(strings.ToLower(data), sensitiveField) {
				event.Request.Data = "[Filtered]"
				break
			}
		}
	}

	return event
}

// CaptureException captures an exception only if Sentry is enabled
func CaptureException(exception error) {
	if sentryEnabled {
		sentry.CaptureException(exception)
	} else {
		// Log locally if Sentry is disabled
		log.Printf("Exception occurred: %v", exception)
	}
}

// CaptureMessage captures a message only if Sentry is enabled
func CaptureMessage(message string, level sentry.Level) {
	if sentryEnabled {
		sentry.CaptureMessage(message)
	} else {
		// Log locally if Sentry is disabled
		switch level {
		case sentry.LevelError:
			log.Printf("ERROR: %s", message)
		case sentry.LevelWarning:
			log.Printf("WARNING: %s", message)
		case sentry.LevelInfo:
			log.Printf("INFO: %s", message)
		default:
			log.Printf("DEBUG: %s", message)
		}
	}
}

// AddBreadcrumb adds a breadcrumb only if Sentry is enabled
func AddBreadcrumb(message, category string, level sentry.Level, data map[string]interface{}) {
	if sentryEnabled {
		sentry.AddBreadcrumb(&sentry.Breadcrumb{
			Message:  message,
			Category: category,
			Level:    level,
			Data:     data,
		})
	}
}

// ConfigureScope configures Sentry scope with additional context
func ConfigureScope(configurator func(scope *sentry.Scope)) {
	if sentryEnabled {
		sentry.ConfigureScope(configurator)
	}
}

// IsSentryEnabled returns whether Sentry is currently enabled
func IsSentryEnabled() bool {
	return sentryEnabled
}

// GetSentryLevel converts string level to sentry.Level
func GetSentryLevel(level string) sentry.Level {
	switch strings.ToLower(level) {
	case "error":
		return sentry.LevelError
	case "warning", "warn":
		return sentry.LevelWarning
	case "info":
		return sentry.LevelInfo
	case "debug":
		return sentry.LevelDebug
	default:
		return sentry.LevelInfo
	}
}

// TestSentryConnection sends a test event to verify Sentry is working
func TestSentryConnection() {
	if sentryEnabled {
		log.Println("Sending test event to Sentry...")
		sentry.CaptureMessage("Test event from rozana-ims-service - Sentry integration working!")
		sentry.Flush(5 * time.Second)
		log.Println("Test event sent and flushed")
	} else {
		log.Println("Sentry is disabled, skipping test event")
	}
}
