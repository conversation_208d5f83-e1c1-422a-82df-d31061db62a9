package models

// Stock represents the inventory data structure stored in Redis
type Stock struct {
	SKUCode           string  `json:"sku_code"`
	Facility          string  `json:"facility"`
	Quantity          int     `json:"quantity"`
	AvailableQuantity float64 `json:"available_quantity"`
	SafetyQuantity    float64 `json:"safety_quantity"`
	LastUpdate        string  `json:"last_update"`
}


// VariantCalculation represents a single variant calculation
type VariantCalculation struct {
	CalculatedQty int `json:"calculated_qty"`
}

// VariantResponse represents the variant calculation response for a single SKU
type VariantResponse map[string]VariantCalculation

// MultiSKUVariantResponse represents the multi-SKU variant calculation response
type MultiSKUVariantResponse struct {
	Data   map[string]VariantResponse `json:"data"`
	Errors map[string]string          `json:"errors,omitempty"`
}
