package main

import (
	"log"
	"strconv"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/wms/go-redis-api/api/routes"
	"github.com/wms/go-redis-api/config"
	"github.com/wms/go-redis-api/services"
)

func main() {
	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize Sentry with flag-based configuration
	if err := config.InitSentry(); err != nil {
		log.Printf("Sentry initialization failed: %v", err)
		// Don't fail the application if Sen<PERSON> fails to initialize
	}
	defer config.FlushSentry()

	// Initialize Redis client
	redisClient := config.InitRedis(cfg)
	defer redisClient.Close()

	// Initialize optional integrations
	tsTimeoutMS, _ := strconv.Atoi(cfg.TypesenseTimeoutMS)
	tsClient := services.NewTypesenseClient(cfg.TypesenseURL, cfg.TypesenseAPIKey, cfg.TypesenseCollection, tsTimeoutMS)
	slack := services.NewSlackNotifier(cfg.SlackWebhookURL, cfg.SlackEnabled)

	// Create Fiber app
	app := fiber.New(fiber.Config{
		ErrorHandler: func(c *fiber.Ctx, err error) error {
			code := fiber.StatusInternalServerError
			if e, ok := err.(*fiber.Error); ok {
				code = e.Code
			}

			// Capture server errors (5xx) in Sentry
			if code >= 500 {
				config.CaptureException(err)
				config.AddBreadcrumb("HTTP Error", "http", config.GetSentryLevel("error"), map[string]interface{}{
					"status_code": code,
					"method":      c.Method(),
					"path":        c.Path(),
				})
			}

			// Return appropriate error message based on environment
			errorMessage := err.Error()
			if cfg.Environment == "production" && code >= 500 {
				errorMessage = "Something went wrong"
			}

			return c.Status(code).JSON(fiber.Map{
				"error": errorMessage,
			})
		},
	})

	// Middleware
	app.Use(cors.New())
	app.Use(logger.New())
	app.Use(config.GetSentryMiddleware())

	// Setup routes with integrations
	routes.SetupRoutes(app, redisClient, tsClient, slack, cfg.TypesenseHealEnabled, cfg.SafetyQuantity)

	// Start server
	log.Printf("Server is running on port %s", cfg.ServerPort)
	log.Fatal(app.Listen(":" + cfg.ServerPort))
}
