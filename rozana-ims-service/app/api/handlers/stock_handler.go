package handlers

import (
	"context"
	"log"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/wms/go-redis-api/models"
	"github.com/wms/go-redis-api/services"
)

// StockHandler handles stock-related API endpoints
type StockHandler struct {
	stockService  *services.StockService
	tsClient      *services.TypesenseClient
	slackNotifier *services.SlackNotifier
	healEnabled   bool
}

// NewStockHandler creates a new stock handler
func NewStockHandler(redisClient *redis.Client, tsClient *services.TypesenseClient, slack *services.SlackNotifier, healEnabled bool, safetyQuantity float64) *StockHandler {
	return &StockHandler{
		stockService:  services.NewStockService(redisClient, safetyQuantity),
		tsClient:      tsClient,
		slackNotifier: slack,
		healEnabled:   healEnabled,
	}
}

// GetMultiSKUVariants handles GET /api/stock/variants endpoint for multi-SKU variant calculation
func (h *StockHandler) GetMultiSKUVariants(c *fiber.Ctx) error {
	// Get mandatory facility query param
	facility := strings.TrimSpace(c.Query("facility"))
	if facility == "" {
		return c.JSON(models.MultiSKUVariantResponse{
			Errors: map[string]string{
				"facility": "Facility is required",
			},
		})
	}

	// Parse SKU-variant pairs from query params
	skuVariants := make(map[string][]string)

	// Iterate through all query parameters to find SKU-variant pairs
	log.Printf("DEBUG: Starting query parameter parsing for facility=%s", facility)
	c.Context().QueryArgs().VisitAll(func(key, value []byte) {
		keyStr := string(key)
		valueStr := string(value)

		log.Printf("DEBUG: Found query param - key='%s', value='%s'", keyStr, valueStr)

		// Skip facility parameter
		if keyStr == "facility" {
			log.Printf("DEBUG: Skipping facility parameter")
			return
		}

		// Parse variants for this SKU
		if valueStr != "" {
			log.Printf("DEBUG: Processing SKU='%s' with value='%s'", keyStr, valueStr)
			variants := strings.Split(valueStr, ",")
			log.Printf("DEBUG: After split by comma: %v", variants)

			// Trim whitespace from variants
			for i, variant := range variants {
				originalVariant := variant
				variants[i] = strings.TrimSpace(variant)
				if originalVariant != variants[i] {
					log.Printf("DEBUG: Trimmed variant[%d]: '%s' -> '%s'", i, originalVariant, variants[i])
				}
			}

			log.Printf("DEBUG: Final variants for SKU '%s': %v", keyStr, variants)
			skuVariants[keyStr] = variants
		} else {
			log.Printf("DEBUG: Empty value for key='%s', skipping", keyStr)
		}
	})

	log.Printf("DEBUG: Final skuVariants map: %+v", skuVariants)

	// Check if any SKUs were provided
	if len(skuVariants) == 0 {
		return c.JSON(models.MultiSKUVariantResponse{
			Errors: map[string]string{
				"skus": "At least one SKU with variants is required",
			},
		})
	}

	// Get multi-SKU variant calculations
	response := h.stockService.GetMultiSKUVariants(c.Context(), facility, skuVariants)

	// Fire-and-forget heal: for each SKU, if variant "1" is zero, trigger Typesense update in background
	if h.healEnabled && h.tsClient != nil && response.Data != nil {
		for sku := range skuVariants {
			if vr, ok := response.Data[sku]; ok {
				if calc, ok := vr["1"]; ok && calc.CalculatedQty == 0 {
					whSKU := sku
					facilityCode := facility

					go func(wh, fac string) {
						ctx, cancel := context.WithTimeout(context.Background(), 4*time.Second)
						defer cancel()

						// Capture BEFORE state from Typesense (best effort)
						beforeAvail, beforeQty, err := h.tsClient.GetStateByWhSKU(ctx, wh, fac)

						log.Printf("error %v", err)

						var lastErr error
						var updatedCount int
						for i := 0; i < 3; i++ {
							if count, err := h.tsClient.UpdateAvailabilityByWhSKU(ctx, wh, fac, false, 0); err == nil {
								updatedCount = count
								log.Printf("typesense heal success wh_sku=%s facility_code=%s updated=%d", wh, fac, updatedCount)

								// After state is known: we set it to false, 0
								afterAvail, afterQty := false, 0

								// Only send Slack alert if before and after states are different
								if h.slackNotifier != nil && (beforeAvail != afterAvail || beforeQty != afterQty) {
									_ = h.slackNotifier.SendHealAlert(ctx, fac, wh, h.tsClient.BaseURL, updatedCount, beforeAvail, beforeQty, afterAvail, afterQty)
									log.Printf("heal alert sent for wh_sku=%s facility_code=%s (state changed: before=[%t,%d] after=[%t,%d])", wh, fac, beforeAvail, beforeQty, afterAvail, afterQty)
								} else if h.slackNotifier != nil {
									log.Printf("heal alert skipped for wh_sku=%s facility_code=%s (no state change: [%t,%d])", wh, fac, beforeAvail, beforeQty)
								}
								return
							} else {
								lastErr = err
								time.Sleep(time.Duration(200*(i+1)) * time.Millisecond)
							}
						}
						log.Printf("typesense heal failed wh_sku=%s facility_code=%s err=%v", wh, fac, lastErr)
					}(whSKU, facilityCode)
				}
			}
		}
	}

	// Return response
	return c.JSON(response)
}
