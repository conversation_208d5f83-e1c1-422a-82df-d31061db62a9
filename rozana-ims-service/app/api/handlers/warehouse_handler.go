package handlers

import (
	"encoding/json"
	"log"
	"os"
	"sort"
	"strconv"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/go-redis/redis/v8"
	"github.com/wms/go-redis-api/services"
)

type WarehouseHandler struct {
	redisClient                *redis.Client
	safetyQuantity             float64
	safetyStockConfigManager   *services.SafetyStockConfigManager
}

// NewWarehouseHandler creates a new warehouse handler
func NewWarehouseHandler(redisClient *redis.Client, safetyQuantity float64) *WarehouseHandler {
	return &WarehouseHandler{
		redisClient:              redisClient,
		safetyQuantity:           safetyQuantity,
		safetyStockConfigManager: services.NewSafetyStockConfigManager(redisClient, safetyQuantity),
	}
}

// WarehouseStats represents warehouse statistics
type WarehouseStats struct {
	Warehouse         string `json:"warehouse"`
	TotalRecords      int    `json:"total_records"`
	GreaterThanZero   int    `json:"greater_than_zero"`
	GreaterThanSafety int    `json:"greater_than_safety_qty"`
}

// SkuData represents the structure of SKU data in Redis
type SkuData struct {
	Data struct {
		// Use json.Number to handle both string and number values
		AvailableQuantity json.Number `json:"available_quantity"`
	} `json:"data"`
}

// GetWarehouseStats returns statistics about warehouse inventory
func (h *WarehouseHandler) GetWarehouseStats(c *fiber.Ctx) error {
	// Check Authorization header
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "Missing Authorization header",
		})
	}

	// Get token from environment variable
	staticToken := os.Getenv("WAREHOUSE_API_TOKEN")
	if staticToken == "" {
		log.Println("Warning: WAREHOUSE_API_TOKEN environment variable not set")
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Server configuration error",
		})
	}

	// Verify the token
	if authHeader != "Bearer "+staticToken {
		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": "Invalid or expired token",
		})
	}

	ctx := c.Context()
	
	// Get warehouse filter from query parameter
	warehouseFilter := c.Query("warehouse", "")
	
	// Build the Redis key pattern based on filter
	var keyPattern string
	if warehouseFilter != "" {
		// Match exact warehouse name with any SKU
		keyPattern = "stock:" + warehouseFilter + ":*"
	} else {
		// Match all warehouses but ensure proper key structure
		keyPattern = "stock:*:*"
	}

	// Use SCAN to find all matching keys (safer for production)
	var cursor uint64
	var keys []string
	for {
		var err error
		var partialKeys []string

		// Scan for keys matching the pattern
		partialKeys, cursor, err = h.redisClient.Scan(ctx, cursor, keyPattern, 1000).Result()
		if err != nil {
			log.Printf("Error scanning keys: %v", err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Failed to scan warehouse data",
			})
		}

		keys = append(keys, partialKeys...)

		// If cursor is 0, we've finished scanning
		if cursor == 0 {
			break
		}
	}

	if len(keys) == 0 {
		// If no keys found, return empty array instead of null
		return c.JSON([]WarehouseStats{})
	}

	warehouseMap := make(map[string]*WarehouseStats)
	processedCount := 0

	// Process keys in batches to avoid memory issues
	batchSize := 100
	for i := 0; i < len(keys); i += batchSize {
		end := i + batchSize
		if end > len(keys) {
			end = len(keys)
		}
		batch := keys[i:end]

		// Get all values in a pipeline
		cmds, err := h.redisClient.Pipelined(ctx, func(pipe redis.Pipeliner) error {
			for _, key := range batch {
				pipe.Get(ctx, key)
			}
			return nil
		})

		if err != nil && err != redis.Nil {
			log.Printf("Error getting values in pipeline: %v", err)
			continue
		}

		// Process the batch
		for _, cmd := range cmds {
			if cmd.Err() != nil {
				log.Printf("Error getting key: %v", cmd.Err())
				continue
			}

			val, ok := cmd.(*redis.StringCmd).Val(), true
			if !ok {
				continue
			}

			// Get the key for this value
			key := batch[processedCount%batchSize]
			parts := strings.Split(key, ":")
			if len(parts) < 3 {
				processedCount++
				continue
			}
			warehouse := parts[1]

			// Initialize warehouse stats if not exists
			if _, exists := warehouseMap[warehouse]; !exists {
				warehouseMap[warehouse] = &WarehouseStats{
					Warehouse:         warehouse,
					TotalRecords:      0,
					GreaterThanZero:   0,
					GreaterThanSafety: 0,
				}
			}

			// Parse the JSON data
			var skuData SkuData
			if err := json.Unmarshal([]byte(val), &skuData); err != nil {
				log.Printf("Error parsing data for key %s: %v", key, err)
				processedCount++
				continue
			}

			// Update stats
			warehouseMap[warehouse].TotalRecords++

			// Convert available_quantity to float64 for comparison
			qty, err := skuData.Data.AvailableQuantity.Float64()
			if err != nil {
				log.Printf("Error converting quantity to float for key %s: %v", key, err)
				processedCount++
				continue
			}

			if qty > 0 {
				warehouseMap[warehouse].GreaterThanZero++
			}
			if qty > h.safetyQuantity {
				warehouseMap[warehouse].GreaterThanSafety++
			}

			processedCount++
		}
	}

	// Convert map to slice and sort by warehouse name
	result := make([]WarehouseStats, 0, len(warehouseMap))
	for _, stats := range warehouseMap {
		result = append(result, *stats)
	}

	// Sort the results by warehouse name
	sort.Slice(result, func(i, j int) bool {
		return result[i].Warehouse < result[j].Warehouse
	})

	return c.JSON(result)
}

// SafetyStockResponse represents the safety stock configuration response
type SafetyStockResponse struct {
	Category       string  `json:"category"`
	SafetyQuantity float64 `json:"safety_quantity"`
	Source         string  `json:"source"` // "redis" or "default"
}

// GetSafetyStockByCategory returns safety stock configuration for a given category
// Endpoint: GET /api/admin/safety-stock?category=Groceries
func (h *WarehouseHandler) GetSafetyStockByCategory(c *fiber.Ctx) error {
	// Check Authorization header
	authHeader := c.Get("Authorization")
	if authHeader == "" {
		return c.Status(fiber.StatusUnauthorized).JSON(fiber.Map{
			"error": "Missing Authorization header",
		})
	}

	// Get token from environment variable
	staticToken := os.Getenv("WAREHOUSE_API_TOKEN")
	if staticToken == "" {
		log.Println("Warning: WAREHOUSE_API_TOKEN environment variable not set")
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Server configuration error",
		})
	}

	// Verify the token
	if authHeader != "Bearer "+staticToken {
		return c.Status(fiber.StatusForbidden).JSON(fiber.Map{
			"error": "Invalid or expired token",
		})
	}

	// Get category from query parameter
	category := strings.TrimSpace(c.Query("category", ""))
	if category == "" {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": "Category is required",
		})
	}

	ctx := c.Context()

	// Try to get from Redis first
	key := "safetystock:" + category
	val, err := h.redisClient.Get(ctx, key).Result()

	response := SafetyStockResponse{
		Category: category,
	}

	if err == redis.Nil {
		// Not found in Redis, use default
		response.SafetyQuantity = h.safetyQuantity
		response.Source = "default"
		log.Printf("SAFETY_STOCK_API: Category '%s' not found in Redis, using environment default = %v", category, h.safetyQuantity)
	} else if err != nil {
		// Error occurred
		log.Printf("SAFETY_STOCK_API: Error retrieving safety stock for category '%s': %v", category, err)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": "Failed to retrieve safety stock configuration",
		})
	} else {
		// Found in Redis, parse the value
		safetyStock, err := strconv.ParseFloat(val, 64)
		if err != nil {
			log.Printf("SAFETY_STOCK_API: Error parsing safety stock value '%s' for category '%s': %v", val, category, err)
			return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
				"error": "Invalid safety stock value in Redis",
			})
		}
		response.SafetyQuantity = safetyStock
		response.Source = "redis"
		log.Printf("SAFETY_STOCK_API: Retrieved safety stock for category '%s' from Redis = %v", category, safetyStock)
	}

	return c.JSON(response)
}
