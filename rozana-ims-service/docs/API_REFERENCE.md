# API Reference - Rozana IMS Service

## Base URL
```
http://localhost:8080/api
```

## Endpoints

### Health Check
**GET** `/health`

Returns service health status.

**Response:**
```json
{
  "status": "ok"
}
```

---

### Stock Lookup with Variant Calculation
**GET** `/stock/:sku`

Retrieves variant calculations for a specific SKU.

**Path Parameters:**
- `sku` (string, required) - The SKU identifier

**Query Parameters:**
- `facility` (string, required) - Warehouse/facility identifier
- `variants` (string, required) - Comma-separated pack sizes (e.g., "6,12,24")

**Example Request:**
```bash
GET /api/stock/sku-1?facility=warehouse1&variants=6,12,24
```

**Success Response (200):**
```json
{
  "6": {"calculated_qty": 8},
  "12": {"calculated_qty": 4},
  "24": {"calculated_qty": 2}
}
```

**Error Responses:**

**400 - Bad Request:**
```json
{
  "success": false,
  "error": "SKU code is required"
}
```

```json
{
  "success": false,
  "error": "Facility is required"
}
```

```json
{
  "success": false,
  "error": "Variants parameter is required"
}
```

```json
{
  "success": false,
  "error": "variant must be greater than 0 and less than 50: 50"
}
```

**500 - Internal Server Error:**
```json
{
  "success": false,
  "error": "SKU not found"
}
```

---

### Multiple SKUs Lookup
**GET** `/stock/multi`

Retrieves stock information for multiple SKUs (legacy endpoint - does not support variants).

**Query Parameters:**
- `skus` (string, required) - Comma-separated SKU codes
- `facility` (string, required) - Warehouse/facility identifier

**Example Request:**
```bash
GET /api/stock/multi?skus=sku-1,sku-2&facility=warehouse1
```

**Response:**
```json
{
  "success": true,
  "count": 2,
  "data": [
    {
      "sku_code": "sku-1",
      "facility": "warehouse1",
      "quantity": 48,
      "available_quantity": 48
    },
    {
      "sku_code": "sku-2", 
      "facility": "warehouse1",
      "quantity": 100,
      "available_quantity": 100
    }
  ]
}
```

## Rate Limits
No rate limits currently implemented.

## Authentication
No authentication required.

## Error Handling
All errors return appropriate HTTP status codes with JSON error messages containing:
- `success`: false
- `error`: Human-readable error message
