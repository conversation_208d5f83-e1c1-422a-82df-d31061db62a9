# Documentation - Rozana IMS Service

This directory contains comprehensive documentation for the Rozana Inventory Management Service (IMS).

## Documentation Files

### 📋 [VARIANT_CALCULATION.md](./VARIANT_CALCULATION.md)
Complete guide to the variant calculation feature including:
- Problem statement and overview
- Calculation logic and formulas
- Validation rules and error handling
- Usage examples and test scenarios
- Implementation details

### 🔌 [API_REFERENCE.md](./API_REFERENCE.md)
Complete API documentation including:
- All available endpoints
- Request/response formats
- Parameter specifications
- Error codes and messages
- Example requests and responses

### 🧪 [TESTING_GUIDE.md](./TESTING_GUIDE.md)
Comprehensive testing documentation including:
- Test data setup instructions
- Complete test case coverage
- Validation and edge case tests
- Performance testing scenarios
- Automated test scripts

## Quick Start

1. **Start the service:**
   ```bash
   docker-compose up --build -d
   ```

2. **Test the variant calculation:**
   ```bash
   # Add test data
   docker exec rozana-ims-service-redis-1 redis-cli SET "stock:warehouse1:sku-1" \
     '{"data": {"sku_code": "sku-1", "total_quantity": 48, "available_quantity": 48}}'
   
   # Test variant calculation
   curl "http://localhost:8080/api/stock/sku-1?facility=warehouse1&variants=6,12,24"
   ```

3. **Expected response:**
   ```json
   {"6":{"calculated_qty":8},"12":{"calculated_qty":4},"24":{"calculated_qty":2}}
   ```

## Key Features

- ✅ **Variant Calculation**: Calculate pack quantities from base inventory
- ✅ **Quantity Masking**: 20 quantity cap for security
- ✅ **Floor Division**: Only complete packs counted
- ✅ **Input Validation**: Comprehensive parameter validation
- ✅ **Error Handling**: Clear error messages and status codes

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UI/Frontend   │───▶│  IMS Service    │───▶│     Redis       │
│                 │    │  (Go/Fiber)     │    │   (Inventory)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Support

For questions or issues:
1. Check the [TESTING_GUIDE.md](./TESTING_GUIDE.md) for troubleshooting
2. Review [API_REFERENCE.md](./API_REFERENCE.md) for endpoint details
3. See [VARIANT_CALCULATION.md](./VARIANT_CALCULATION.md) for feature specifics
