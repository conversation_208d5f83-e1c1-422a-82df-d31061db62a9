# Multi-SKU Variant Calculation API

## Overview

The rozana-ims-service now supports multi-SKU variant calculation functionality that allows calculating available quantities for different pack sizes across multiple SKUs in a single API request. This replaces the single SKU variant calculation endpoint.

## Problem Statement

The IMS system stores inventory for 1-piece variants (e.g., "sku-1-pcs"), but the UI needs to display availability for different pack sizes like 6-pcs, 12-pcs, and 24-pcs variants across multiple SKUs efficiently. This feature calculates how many complete packs can be formed from the available base quantity for multiple SKUs simultaneously.

## API Endpoint

### GET `/api/stock/variants`

**Required Parameters:**
- `facility` (query param) - The warehouse/facility identifier
- `{sku}` (query params) - Each SKU with its comma-separated list of variant sizes

**Example Request:**
```bash
GET /api/stock/variants?facility=warehouse1&sku-1=6,12,24&sku-2=6,12,48&sku-3=12,24
```

**Example Response (Success):**
```json
{
  "data": {
    "sku-1": {
      "6": {"calculated_qty": 8},
      "12": {"calculated_qty": 4},
      "24": {"calculated_qty": 2}
    },
    "sku-2": {
      "6": {"calculated_qty": 16},
      "12": {"calculated_qty": 8},
      "48": {"calculated_qty": 2}
    },
    "sku-3": {
      "12": {"calculated_qty": 4},
      "24": {"calculated_qty": 2}
    }
  }
}
```

**Example Response (Partial Failure):**
```json
{
  "data": {
    "sku-1": {
      "6": {"calculated_qty": 8},
      "12": {"calculated_qty": 4},
      "24": {"calculated_qty": 2}
    }
  },
  "errors": {
    "nonexistent-sku": "SKU not found",
    "sku-3": "invalid variant value: abc"
  }
}
```

## Calculation Logic

### Formula
```
calculated_qty = min(floor(available_quantity / variant_size), 20)
```

### Calculation Examples

| Available Qty | Variant Size | Calculation | Result | Final (Cap Applied) |
|---------------|--------------|-------------|--------|-------------------|
| 48 | 6 | floor(48 ÷ 6) | 8 | 8 |
| 48 | 12 | floor(48 ÷ 12) | 4 | 4 |
| 48 | 24 | floor(48 ÷ 24) | 2 | 2 |
| 1000 | 6 | floor(1000 ÷ 6) | 166 | **20** (capped) |
| 1000 | 12 | floor(1000 ÷ 12) | 83 | **20** (capped) |
| 1000 | 24 | floor(1000 ÷ 24) | 41 | **20** (capped) |
| 10 | 12 | floor(10 ÷ 12) | 0 | 0 |
| 10 | 24 | floor(10 ÷ 24) | 0 | 0 |

### Quantity Masking Rules
- **20 Quantity Cap**: Final calculated quantities are capped at 20 to mask exact inventory levels
- **Floor Division**: Always rounds down to ensure only complete packs are counted

## Validation Rules

### Variant Validation
- Must be greater than 0
- Must be less than 50
- Cannot be empty or missing

### Error Responses

**Missing variants parameter:**
```json
{
  "success": false,
  "error": "Variants parameter is required"
}
```

**Invalid variant value:**
```json
{
  "success": false,
  "error": "variant must be greater than 0 and less than 50: 50"
}
```

**SKU not found:**
```json
{
  "success": false,
  "error": "SKU not found"
}
```

## Usage Examples

### Multi-SKU Request
```bash
curl "http://localhost:8080/api/stock/variants?facility=warehouse1&sku-1=6,12,24&sku-2=6,12,48"
```

### Single SKU with Multiple Variants
```bash
curl "http://localhost:8080/api/stock/variants?facility=warehouse1&sku-1=6,12,24,48"
```

### Multiple SKUs with Different Variants
```bash
curl "http://localhost:8080/api/stock/variants?facility=warehouse1&sku-1=6,12&sku-2=12,24&sku-3=6,48"
```

## Test Scenarios

### Test Data Setup
```bash
# Add test data to Redis with actual format
docker exec rozana-ims-service-redis-1 redis-cli SET "stock:ROZANA_TEST_WH1:ROZ4953" \
  '{"warehouse":"ROZANA_TEST_WH1","data":{"sku_code":"ROZ4953","sku_desc":"Britannia Good Day Cashew Cookies","seller_id":null,"seller_name":null,"cost_price":"0.0","batch_quantity":"0.0","is_scannable":"0","serial_number":[],"total_quantity":30.0,"reserved_quantity":"0.0","open_order_quantity":"0","isn_quantity":"0","putaway_quantity":"0","cycle_stock_quantity":"0","staging_stock_quantity":"0","available_quantity":30.0,"batch_details":[]}}'

docker exec rozana-ims-service-redis-1 redis-cli SET "stock:ROZANA_TEST_WH1:ROZ16721-XL" \
  '{"warehouse":"ROZANA_TEST_WH1","data":{"sku_code":"ROZ16721-XL","sku_desc":"Test Product XL","seller_id":null,"seller_name":null,"cost_price":"0.0","batch_quantity":"0.0","is_scannable":"0","serial_number":[],"total_quantity":48.0,"reserved_quantity":"0.0","open_order_quantity":"0","isn_quantity":"0","putaway_quantity":"0","cycle_stock_quantity":"0","staging_stock_quantity":"0","available_quantity":48.0,"batch_details":[]}}'
```

### Expected Results

| Test Case | Available Qty | Request | Expected Response |
|-----------|---------------|---------|-------------------|
| **Multi-SKU Calculation** | ROZ4953: 30, ROZ16721-XL: 48 | `ROZ4953=6,12,24&ROZ16721-XL=6,12,48` | `{"data":{"ROZ4953":{"6":{"calculated_qty":5},"12":{"calculated_qty":2},"24":{"calculated_qty":1}},"ROZ16721-XL":{"6":{"calculated_qty":8},"12":{"calculated_qty":4},"48":{"calculated_qty":1}}}}` |
| **Single SKU Multiple Variants** | ROZ4953: 30 | `ROZ4953=6,12,24` | `{"data":{"ROZ4953":{"6":{"calculated_qty":5},"12":{"calculated_qty":2},"24":{"calculated_qty":1}}}}` |
| **Partial Failure** | ROZ4953: 30 | `ROZ4953=6,12&nonexistent=6,12` | `{"data":{"ROZ4953":{"6":{"calculated_qty":5},"12":{"calculated_qty":2}}},"errors":{"nonexistent":"SKU not found"}}` |
| **Quantity Cap Applied** | High quantity SKU | `high-qty-sku=6,12,24` | `{"data":{"high-qty-sku":{"6":{"calculated_qty":20},"12":{"calculated_qty":20},"24":{"calculated_qty":20}}}}` |

### Validation Test Cases

| Test Type | Request | Expected Error |
|-----------|---------|----------------|
| **Invalid Range (High)** | `sku-1=50` | "variant must be greater than 0 and less than 50: 50" |
| **Invalid Range (Low)** | `sku-1=0` | "variant must be greater than 0 and less than 50: 0" |
| **Non-numeric** | `sku-1=abc` | "invalid variant value: abc" |
| **Negative Value** | `sku-1=-5` | "variant must be greater than 0 and less than 50: -5" |
| **Missing Facility** | No facility param | "Facility is required" |
| **Missing SKUs** | Only facility param | "At least one SKU with variants is required" |
| **Non-existent SKU** | `nonexistent-sku=6,12` | "SKU not found" |

## Implementation Details

### Files Modified
- `app/models/stock.go` - Added `MultiSKUVariantResponse`, `VariantCalculation` and `VariantResponse` models
- `app/services/stock_service.go` - Added `GetMultiSKUVariants()` method
- `app/api/handlers/stock_handler.go` - Implemented `GetMultiSKUVariants()` handler
- `app/api/routes/routes.go` - Updated routes to use single `/variants` endpoint

### Key Components

**Multi-SKU Response Model:**
```go
type VariantCalculation struct {
    CalculatedQty int `json:"calculated_qty"`
}

type VariantResponse map[string]VariantCalculation

type MultiSKUVariantResponse struct {
    Data   map[string]VariantResponse `json:"data"`
    Errors map[string]string          `json:"errors,omitempty"`
}
```

**Service Method:**
```go
func (s *StockService) GetMultiSKUVariants(ctx context.Context, facility string, skuVariants map[string][]string) models.MultiSKUVariantResponse
```

### Redis Key Format
```
stock:{facility}:{sku}
```

Example: `stock:ROZANA_TEST_WH1:ROZ4953`

### Redis Data Structure
```json
{
  "warehouse": "ROZANA_TEST_WH1",
  "data": {
    "sku_code": "ROZ4953",
    "sku_desc": "Britannia Good Day Cashew Cookies",
    "seller_id": null,
    "seller_name": null,
    "cost_price": "0.0",
    "batch_quantity": "0.0",
    "is_scannable": "0",
    "serial_number": [],
    "total_quantity": 30.0,
    "reserved_quantity": "0.0",
    "open_order_quantity": "0",
    "isn_quantity": "0",
    "putaway_quantity": "0",
    "cycle_stock_quantity": "0",
    "staging_stock_quantity": "0",
    "available_quantity": 30.0,
    "batch_details": []
  }
}
```

**Key Fields Used:**
- `warehouse`: Facility/warehouse identifier
- `data.sku_code`: SKU identifier
- `data.total_quantity`: Total inventory quantity
- `data.available_quantity`: Available quantity for variant calculations

## Deployment

The service runs via Docker Compose:

```bash
# Start services
docker-compose up --build -d

# Check health
curl http://localhost:8080/api/health

# Test multi-SKU variant calculation
curl "http://localhost:8080/api/stock/variants?facility=ROZANA_TEST_WH1&ROZ4953=6,12,24&ROZ16721-XL=6,12,48"
```

## Notes

- Single unified API endpoint for all variant calculations
- Supports multiple SKUs in a single request for improved performance
- Partial failure handling - successful calculations returned alongside specific errors
- All validation rules are enforced at the service layer
- Floor division ensures only complete packs are counted
- The 20 quantity cap helps mask exact inventory levels for security
- Response format excludes original available quantity as requested
- Legacy endpoints have been removed to maintain a clean API surface
