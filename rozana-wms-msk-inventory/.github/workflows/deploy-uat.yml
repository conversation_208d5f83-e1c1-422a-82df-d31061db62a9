name: Deploy <PERSON>da to UAT

on:
  workflow_dispatch:
    inputs:
      image_tag:
        description: "ECR image tag to deploy (example: v1.0.0)"
        required: true
        default: latest

jobs:
  deploy-uat:
    runs-on: oms-runner

    env:
      AWS_REGION: ${{ vars.AWS_REGION }}
      ECR_REPOSITORY: ${{ vars.ECR_REPOSITORY }}
      ECR_REGISTRY: ${{ vars.ECR_REGISTRY }}
      LAMBDA_FUNCTION_NAME: rozana-inventory-wms-lamda-uat

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Deploy to UAT Lambda
        run: |
          IMAGE_URI=$ECR_REGISTRY/$ECR_REPOSITORY:${{ github.event.inputs.image_tag }}
          echo "🚀 Deploying UAT Lambda with $IMAGE_URI"
          aws lambda update-function-code \
            --function-name $LAMBDA_FUNCTION_NAME \
            --image-uri $IMAGE_URI
