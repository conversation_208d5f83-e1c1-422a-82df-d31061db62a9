import json
import redis
import os
import logging

logger = logging.getLogger(__name__)

REDIS_URL = os.environ.get('REDIS_URL', "redis://localhost:6379")

class RedisJSONWrapper:
    def __init__(self, redis_uri=REDIS_URL, database=None):
        if database is not None:
            redis_uri = f"{redis_uri}/{database}"
        try:
            self.redis_client = redis.from_url(redis_uri)
            self.redis_client.ping()
        except redis.exceptions.RedisError as e:
            logger.error(f"Failed to connect to Redis at {redis_uri}: {e}")
            raise

    def set(self, key, data):
        self.redis_client.set(key, json.dumps(data))

    def get(self, key):
        data = self.redis_client.get(key)
        if data:
            return json.loads(data)
        return None

    def delete(self, key):
        return self.redis_client.delete(key) > 0

    def keys(self, pattern='*'):
        return [key.decode('utf-8') for key in self.redis_client.keys(pattern)]

    def delete_keys_with_suffix(self, suffix):
        pattern = f"*{suffix}"
        matching_keys = self.keys(pattern)
        for key in matching_keys:
            self.delete(key)
            print(f"Deleted key: {key}")
        return len(matching_keys)
