import os
import json
import logging
import requests
from typing import Dict

logger = logging.getLogger(__name__)

class TypesenseWrapper:
    def __init__(self):
        """Initialize TypesenseWrapper with environment variables."""
        self.api_url = os.environ.get('TYPESENSE_URL', '')
        self.api_key = os.environ.get('TYPESENSE_API_KEY', '')
        self.collection = os.environ.get('TYPESENSE_COLLECTION', '')
        self.freebies_collection = os.environ.get('TYPESENSE_FREEBIES_COLLECTION', 'freebies_products')

    def _get_headers(self) -> Dict[str, str]:
        """Return headers for regular Typesense API requests (search, single updates)."""
        return {
            'X-TYPESENSE-API-KEY': self.api_key,
            'Content-Type': 'application/json'
        }

    def _get_bulk_headers(self) -> Dict[str, str]:
        """Return headers for bulk operations (JSONL format)."""
        return {
            'X-TYPESENSE-API-KEY': self.api_key,
            'Content-Type': 'text/plain'
        }

    def _get_collection_for_sku(self, sku_code: str) -> str:
        """Determine which collection to use based on SKU code.
        Args:
            sku_code: The SKU code to check
        Returns:
            Collection name - freebies_products for FRB SKUs, default collection otherwise
        """
        if sku_code.startswith('FRB'):
            return self.freebies_collection
        return self.collection

    def search_documents(self, query_params: Dict[str, str], collection: str = None) -> Dict:
        """Search documents in Typesense collection.

        Args:
            query_params: Parameters for the search query
            collection: Optional collection name, defaults to self.collection
        Returns:
            The search response as a dictionary
        """
        target_collection = collection or self.collection
        url = f"{self.api_url}/collections/{target_collection}/documents/search"

        try:
            response = requests.get(
                url,
                headers=self._get_headers(),
                params=query_params
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"TYPESENSE_API_ERROR: Error searching documents: {e}")
            return {"error": str(e)}

    def get_document(self, document_id: str, collection: str = None) -> Dict:
        """Get a specific document by ID.

        Args:
            document_id: The ID of the document to retrieve
            collection: Optional collection name, defaults to self.collection

        Returns:
            The document as a dictionary
        """
        target_collection = collection or self.collection
        url = f"{self.api_url}/collections/{target_collection}/documents/{document_id}"

        try:
            response = requests.get(
                url,
                headers=self._get_headers()
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"TYPESENSE_API_ERROR: Error getting document {document_id}: {e}")
            return {"error": str(e)}

    def update_document(self, document_id: str, data: Dict, collection: str = None) -> Dict:
        """Update a document by ID with partial data.

        Args:
            document_id: The ID of the document to update
            data: The data to update in the document
            collection: Optional collection name, defaults to self.collection

        Returns:
            The update response as a dictionary
        """
        target_collection = collection or self.collection
        url = f"{self.api_url}/collections/{target_collection}/documents/{document_id}"

        try:
            response = requests.patch(
                url,
                headers=self._get_headers(),
                data=json.dumps(data)
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"TYPESENSE_API_ERROR: Error updating document {document_id}: {e}")
            return {"error": str(e)}

    def find_by_facility_code_and_sku(self, facility_code: str, wh_sku: str) -> Dict:
        """Find documents by facility_code and wh_sku.

        Args:
            facility_code: Facility code to filter by
            wh_sku: Warehouse SKU code to filter by

        Returns:
            The search response with matching documents
        """
        # Determine collection based on SKU
        target_collection = self._get_collection_for_sku(wh_sku)

        filter_by = f"facility_code:={facility_code}&&wh_sku:=`{wh_sku}`"

        query_params = {
            'q': '*',
            'filter_by': filter_by,
            'page': '1',
            'per_page': '15'
        }

        logger.info(f"TYPESENSE_API: Searching in collection '{target_collection}' for facility_code:{facility_code}, wh_sku:{wh_sku}")
        return self.search_documents(query_params, collection=target_collection)

    def bulk_update_documents(self, documents_data: list, collection: str = None) -> Dict:
        """Bulk update multiple documents in Typesense collection.
        
        Performs PARTIAL UPDATE - only updates the specified fields, preserves all other fields.
        Same behavior as individual PATCH operations.
        Args:
            documents_data: List of documents with id and fields to update
                          Format: [{'id': 'doc1', 'is_available': True, 'available_qty': 10}, ...]
            collection: Optional collection name, defaults to self.collection

        Returns:
            The bulk update response as a dictionary
        """
        target_collection = collection or self.collection
        url = f"{self.api_url}/collections/{target_collection}/documents/import"

        # Convert to JSONL format (one JSON object per line)
        jsonl_data = '\n'.join(json.dumps(doc) for doc in documents_data)
        logger.info(f"TYPESENSE_API: Bulk updating {len(documents_data)} documents in collection '{target_collection}': {jsonl_data}")

        try:
            response = requests.post(
                url,
                headers=self._get_bulk_headers(),
                data=jsonl_data,
                params={'action': 'update'}
            )
            response.raise_for_status()

            # Parse response - Typesense returns JSONL for bulk operations
            results = []
            for line in response.text.strip().split('\n'):
                if line.strip():
                    results.append(json.loads(line))

            return {'results': results, 'success_count': len([r for r in results if 'success' in r and r['success']])}
        except requests.exceptions.RequestException as e:
            logger.error(f"TYPESENSE_API_ERROR: Error bulk updating documents: {e}")
            return {"error": str(e)}
