from app.redis_wrapper import Redis<PERSON><PERSON><PERSON>Wrapper
from app.typesense_wrapper import TypesenseWrapper
from app.inventory_stock_processor import InventoryStockMessageProcessor
import json
import base64
import logging
import traceback
import os

# Environment variable to control decoding behavior
PAYLOAD_DECODE = os.environ.get('PAYLOAD_DECODE', 'true').lower() == 'true'

# Configure logging based on environment
log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
logger = logging.getLogger()
logger.setLevel(getattr(logging, log_level))

# Initialize components - lazy loading to avoid connection during cold start
redis_wrapper = None
typesense_wrapper = None
processor = None

def get_processor():
    """Get or initialize the inventory stock processor"""
    global redis_wrapper, typesense_wrapper, processor
    if processor is None:
        logger.info("Initializing Redis wrapper, Typesense wrapper and inventory stock processor")
        redis_wrapper = RedisJSONWrapper()
        typesense_wrapper = TypesenseWrapper()
        processor = InventoryStockMessageProcessor(redis_wrapper, typesense_wrapper)
    return processor

def lambda_handler(event, context):
    """
    Process Kafka messages and update both Redis and Typesense

    Event structure from MSK/Kafka:
    {
        "records": {
            "topic-name": [
                {
                    "value": "base64-encoded-json-string",
                    "headers": [
                        {"warehouse_name": [byte values]},
                        {"timestamp": [byte values]},
                        {"idempotent_key": [byte values]}
                    ]
                }
            ]
        }
    }

    The decoded value will be a JSON object like:
    {
        "sku_code": "SKU-1",
        "total_quantity": 500.0,
        "available_quantity": 431.0,
        ...
    }

    The headers will be decoded to:
    {
        "warehouse_name": "TEST_WH1",
        "timestamp": "2025-06-23T02:20:55.747056+00:00",
        "idempotent_key": "60821870-4b73-4c0c-9e44-f0f2825d3b2d"
    }
    """
    logger.info(f"Received event: {json.dumps(event)}")

    # Get processor instance
    current_processor = get_processor()

    # MSK/Kafka events have a different structure - records is a dict with topic names as keys
    records_dict = event.get('records', {})
    if not records_dict:
        logger.error("No records found in event")
        return {"statusCode": 200, "body": json.dumps({"error": "No records found"})}

    # Process each topic
    for topic_name, records in records_dict.items():
        logger.info(f"Processing topic: {topic_name} with {len(records)} records")

        # Process each record in the topic
        for record in records:
            try:
                logger.info(f"Processing record: {json.dumps(record)}")

                # Validate record structure
                if not isinstance(record, dict):
                    logger.error(f"Error: Record is not a dictionary, type={type(record).__name__}, value={record}")
                    continue

                if 'value' not in record:
                    logger.error(f"Error: No 'value' key in record: {record}")
                    continue

                # Decode the Kafka record payload
                payload = decode_kafka_record(record)
                if payload is None:
                    continue

                # Extract and decode Kafka headers
                headers = extract_kafka_headers(record)
                if headers is None:
                    continue

                # Get required fields
                warehouse = headers.get('warehouse_name')
                timestamp = headers.get('timestamp')
                idempotent_key = headers.get('idempotent_key')

                # Validate we have required fields
                if not warehouse:
                    logger.error("No warehouse_name found in headers")
                    continue

                if not timestamp:
                    logger.error("No timestamp found in headers")
                    continue

                # Extract SKU from payload
                sku_code = None
                if 'data' in payload and 'sku_code' in payload['data']:
                    sku_code = payload['data']['sku_code']
                elif 'sku_code' in payload:
                    sku_code = payload['sku_code']

                if not sku_code:
                    logger.error(f"No sku_code found in payload: {payload}")
                    continue

                logger.info(f"Processing stock update for warehouse {warehouse} SKU {sku_code} timestamp {timestamp}")

                force_typesense_update = payload.get('data', {}).get('force_sync', False)
                # Process the stock message using inventory processor
                result = current_processor.process(warehouse, sku_code, payload, timestamp, idempotent_key, force_typesense_update)

                if result:
                    logger.info(f"Successfully processed stock update for {warehouse}:{sku_code}")
                else:
                    logger.info(f"Stock update skipped for {warehouse}:{sku_code} (no changes needed or older timestamp)")

            except Exception as e:
                logger.error(f"Error processing record: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                # Continue processing other records even if one fails
                continue

    return {"statusCode": 200, "body": json.dumps({"message": "Processing completed"})}

def decode_kafka_record(record):
    """Decode Kafka record payload with optional decoding control"""
    try:
        value = record['value']
        
        if PAYLOAD_DECODE:
            # Decode from base64
            decoded_bytes = base64.b64decode(value)
            decoded_str = decoded_bytes.decode('utf-8')
            payload = json.loads(decoded_str)
            logger.info(f"Decoded payload: {json.dumps(payload)}")
        else:
            # Assume it's already a JSON string or dict
            if isinstance(value, str):
                payload = json.loads(value)
            else:
                payload = value
            logger.info(f"Direct payload: {json.dumps(payload)}")
        
        return payload
    except Exception as e:
        logger.error(f"Error decoding record value: {str(e)}")
        return None

def extract_kafka_headers(record):
    """Extract and decode Kafka headers with optional decoding control"""
    try:
        headers_list = record.get('headers', [])
        headers = {}
        
        for header_item in headers_list:
            for key, value_bytes in header_item.items():
                if PAYLOAD_DECODE:
                    # Decode bytes to string
                    if isinstance(value_bytes, list):
                        # Convert list of bytes to string
                        value_str = ''.join(chr(b) for b in value_bytes)
                    else:
                        value_str = str(value_bytes)
                else:
                    # Use value as-is
                    value_str = str(value_bytes)
                
                headers[key] = value_str
        
        logger.info(f"Extracted headers: {json.dumps(headers)}")
        return headers
    except Exception as e:
        logger.error(f"Error extracting headers: {str(e)}")
        return None
