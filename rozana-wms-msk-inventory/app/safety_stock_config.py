import logging
import os
from typing import Optional

logger = logging.getLogger(__name__)

# Default safety stock from environment
DEFAULT_SAFETY_QUANTITY = int(os.environ.get('SAFETY_QUANTITY', '10'))


class SafetyStockConfigManager:
    """
    Manages category-level safety stock configuration with priority-based resolution.

    Priority Order (Highest to Lowest):
    1. sub_sub_category (safetystock:<sub_sub_category>)
    2. sub_category (safetystock:<sub_category>)
    3. category (safetystock:<category>)
    4. Environment default (SAFETY_QUANTITY)

    Redis Key Format: safetystock:<category_value>
    Example:
        safetystock:Milk & Cream → 2
        safetystock:Dairy Products → 3
        safetystock:Groceries → 4
    """

    SAFETY_STOCK_KEY_PREFIX = "safetystock"

    def __init__(self, redis_client):
        """
        Initialize SafetyStockConfigManager with Redis client.
        Args:
            redis_client: Redis client instance for config storage
        """
        self.redis_client = redis_client

    @staticmethod
    def _build_config_key(category_value: str) -> str:
        """
        Build Redis key for safety stock configuration.
        Args:
            category_value: Category value (category, sub_category, or sub_sub_category)

        Returns:
            Redis key in format: safetystock:<category_value>
        """
        return f"{SafetyStockConfigManager.SAFETY_STOCK_KEY_PREFIX}:{category_value}"

    def get_safety_stock(self, category: Optional[str] = None, sub_category: Optional[str] = None, sub_sub_category: Optional[str] = None) -> int:
        """
        Get safety stock value based on category hierarchy with priority resolution.

        Priority Order:
        1. sub_sub_category (highest priority)
        2. sub_category
        3. category
        4. Environment default SAFETY_QUANTITY (lowest priority)

        Args:
            category: Category name (from collection_name in Typesense)
            sub_category: Sub-category name (from parent_name in Typesense)
            sub_sub_category: Sub-sub-category name (from category_name in Typesense)

        Returns:
            Safety stock value as integer
        """
        # Priority 1: Check sub_sub_category (highest priority)
        if sub_sub_category:
            safety_stock = self._get_from_redis(sub_sub_category)
            if safety_stock is not None:
                logger.info(f"SAFETY_STOCK_CONFIG: Using sub_sub_category config - sub_sub_category='{sub_sub_category}', safety_stock={safety_stock}")
                return safety_stock

        # Priority 2: Check sub_category
        if sub_category:
            safety_stock = self._get_from_redis(sub_category)
            if safety_stock is not None:
                logger.info(f"SAFETY_STOCK_CONFIG: Using sub_category config - sub_category='{sub_category}', safety_stock={safety_stock}")
                return safety_stock

        # Priority 3: Check category
        if category:
            safety_stock = self._get_from_redis(category)
            if safety_stock is not None:
                logger.info(f"SAFETY_STOCK_CONFIG: Using category config - category='{category}', safety_stock={safety_stock}")
                return safety_stock

        # Priority 4: Fall back to environment default (lowest priority)
        logger.info(f"SAFETY_STOCK_CONFIG: No category-level config found, using environment default - category='{category}', sub_category='{sub_category}', sub_sub_category='{sub_sub_category}', safety_stock={DEFAULT_SAFETY_QUANTITY}")
        return DEFAULT_SAFETY_QUANTITY

    def _get_from_redis(self, category_value: str) -> Optional[int]:
        """
        Fetch safety stock value from Redis for a specific category.
        Args:
            category_value: Category value to look up

        Returns:
            Safety stock value as integer, or None if not found
        """
        try:
            key = self._build_config_key(category_value)
            value = self.redis_client.get(key)

            if value is not None:
                # Redis returns bytes, decode to string then convert to int
                if isinstance(value, bytes):
                    value = value.decode('utf-8')
                return int(value)

            return None
        except Exception as e:
            logger.error(f"SAFETY_STOCK_CONFIG: Error fetching from Redis for '{category_value}': {e}")
            return None
