# Inventory Lambda for WMS MSK Processing

A unified AWS Lambda function that processes WMS inventory updates from MSK/Kafka topics, maintaining real-time synchronization between Redis cache and Typesense search engine.

## Architecture

**Inventory Processing Pipeline:**
- **MSK Consumer**: Processes inventory updates from Kafka topics
- **Redis Integration**: Fast cache for previous inventory state
- **Typesense Updates**: Search engine synchronization with bulk operations
- **Business Logic**: Configurable safety stock and display quantity rules

## Processing Flow

1. **MSK Message Consumption**: Receive inventory updates from Kafka topic
2. **Timestamp Validation**: Ensure message is newer than last processed update
3. **Redis Cache Read**: Retrieve previous inventory state for comparison
4. **Business Logic Application**: Calculate availability and display quantities
5. **Typesense Bulk Update**: Synchronize search documents when changes detected
6. **Redis State Update**: Store latest inventory data and processing timestamp

## Business Logic

### Quantity Calculation
- **Availability**: `is_available = wms_quantity > SAFETY_QUANTITY`
- **Display Quantity**: `typesense_qty = min(wms_quantity - SAFETY_QUANTITY, MAX_DISPLAY_QUANTITY)`
- **Safety Stock**: Reserved quantity not shown to customers
- **Display Cap**: Maximum quantity shown to prevent overselling

### Update Triggers
Typesense is updated when either:
- Availability status changes (`is_available` flips)
- Display quantity changes (within the displayable range)

## Key Features

- **High Performance**: Redis-based state management for fast lookups
- **Scalable Architecture**: Single lambda handles both cache and search updates
- **Configurable Business Rules**: Environment-driven safety stock and display limits
- **Bulk Operations**: Efficient Typesense updates for multiple documents
- **Comprehensive Monitoring**: Structured logging with `INVENTORY_PROCESSOR` prefix
- **Idempotent Processing**: Duplicate message handling with timestamp validation

## Environment Variables

### Core Configuration
- `REDIS_URL`: Redis connection string
- `TYPESENSE_URL`: Typesense API endpoint
- `TYPESENSE_API_KEY`: Typesense authentication key
- `TYPESENSE_COLLECTION`: Target collection name

### Processing Configuration
- `LOG_LEVEL`: Logging verbosity (default: INFO)
- `PAYLOAD_DECODE`: Enable base64 payload decoding (default: true)
- `SAFETY_QUANTITY`: Minimum stock threshold (default: 10)
- `MAX_DISPLAY_QUANTITY`: Maximum displayed quantity (default: 20)

## Data Storage

### Redis Key Structure
- **Stock Data**: `stock:{warehouse}:{sku}` - Current inventory payload
- **Timestamp**: `stock_ts:{warehouse}:{sku}` - Last update timestamp

### Typesense Document Fields
- **`is_available`**: Boolean availability status
- **`available_qty`**: Display quantity (capped and safety-adjusted)

## Local Development

```bash
# Copy environment template
cp .env.sample .env

# Edit with your configuration
vim .env

# Start services
docker-compose up --build

# Test with sample message
curl -X POST http://localhost:9000/2015-03-31/functions/function/invocations \
  -d '{"records": {"stock-updates": [...]}}'
```

## Deployment

```bash
# Build for AWS Lambda
docker build -t inventory-lambda .

# Deploy using your CI/CD pipeline
# Configure MSK trigger and environment variables
```

## Monitoring

- **Log Prefix**: `INVENTORY_PROCESSOR:` for easy CloudWatch filtering
- **Key Metrics**: Processing time, Redis hits/misses, Typesense update success rate
- **Error Handling**: Graceful degradation with Redis-only updates on Typesense failures
