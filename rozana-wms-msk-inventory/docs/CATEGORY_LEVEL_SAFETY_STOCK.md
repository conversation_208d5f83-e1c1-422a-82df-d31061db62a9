# Category-Level Safety Stock Configuration

## Overview

Dynamic safety stock management based on product category hierarchy. Instead of using a single global `SAFETY_QUANTITY`, the system resolves safety stock values from Redis configuration with priority-based hierarchy.

## Architecture

| Component | Purpose | Location |
|-----------|---------|----------|
| **SafetyStockConfigManager** | Manages Redis-backed category-level safety stock config | `app/safety_stock_config.py` |
| **InventoryStockMessageProcessor** | Processes stock updates with dynamic safety stock | `app/inventory_stock_processor.py` |
| **Redis** | Stores category-level safety stock configurations | External service |

## Priority Resolution Order

| Priority | Level | Redis Key Format | Example |
|----------|-------|------------------|---------|
| 1 (Highest) | sub_sub_category | `safetystock:<sub_sub_category>` | `safetystock:Milk & Cream` |
| 2 | sub_category | `safetystock:<sub_category>` | `safetystock:Dairy Products` |
| 3 | category | `safetystock:<category>` | `safetystock:Groceries` |
| 4 (Lowest) | Environment Default | `SAFETY_QUANTITY` from .env | Default: 10 |

## Configuration Examples

### Example 1: Fresh Milk Product

| Field | Value |
|-------|-------|
| SKU | ROZ001 |
| Category | Groceries |
| Sub-Category | Dairy Products |
| Sub-Sub-Category | Milk & Cream |
| Available Quantity | 15 |

**Redis Configuration:**
```bash
redis-cli SET "safetystock:Milk & Cream" 2
redis-cli SET "safetystock:Dairy Products" 3
redis-cli SET "safetystock:Groceries" 4
```

**Resolution:**
| Step | Check | Found | Result |
|------|-------|-------|--------|
| 1 | `safetystock:Milk & Cream` | ✓ 2 | **USE THIS** |
| 2 | (Skipped) | - | - |
| 3 | (Skipped) | - | - |
| 4 | (Skipped) | - | - |

**Calculation:**
| Metric | Value |
|--------|-------|
| Safety Stock | 2 |
| is_available | 15 > 2 = TRUE |
| available_qty | min(15 - 2, 20) = 13 |

### Example 2: Organic Vegetables

| Field | Value |
|-------|-------|
| SKU | ROZ002 |
| Category | Groceries |
| Sub-Category | Vegetables |
| Sub-Sub-Category | (empty) |
| Available Quantity | 50 |

**Redis Configuration:**
```bash
redis-cli SET "safetystock:Vegetables" 5
redis-cli SET "safetystock:Groceries" 4
```

**Resolution:**
| Step | Check | Found | Result |
|------|-------|-------|--------|
| 1 | `safetystock:<empty>` | ✗ | Continue |
| 2 | `safetystock:Vegetables` | ✓ 5 | **USE THIS** |
| 3 | (Skipped) | - | - |
| 4 | (Skipped) | - | - |

**Calculation:**
| Metric | Value |
|--------|-------|
| Safety Stock | 5 |
| is_available | 50 > 5 = TRUE |
| available_qty | min(50 - 5, 20) = 20 |

### Example 3: No Category Config (Fallback)

| Field | Value |
|-------|-------|
| SKU | ROZ003 |
| Category | Electronics |
| Sub-Category | (empty) |
| Sub-Sub-Category | (empty) |
| Available Quantity | 25 |

**Redis Configuration:**
```bash
# No config for Electronics
```

**Resolution:**
| Step | Check | Found | Result |
|------|-------|-------|--------|
| 1 | `safetystock:<empty>` | ✗ | Continue |
| 2 | `safetystock:<empty>` | ✗ | Continue |
| 3 | `safetystock:Electronics` | ✗ | Continue |
| 4 | Environment Default | ✓ 10 | **USE THIS** |

**Calculation:**
| Metric | Value |
|--------|-------|
| Safety Stock | 10 (env default) |
| is_available | 25 > 10 = TRUE |
| available_qty | min(25 - 10, 20) = 15 |

## Redis Commands

### Setting Safety Stock

| Command | Purpose | Example |
|---------|---------|---------|
| `redis-cli SET "safetystock:<category>" <value>` | Set category-level safety stock | `redis-cli SET "safetystock:Groceries" 4` |
| `redis-cli SET "safetystock:<sub_category>" <value>` | Set sub-category-level safety stock | `redis-cli SET "safetystock:Dairy Products" 3` |
| `redis-cli SET "safetystock:<sub_sub_category>" <value>` | Set sub-sub-category-level safety stock | `redis-cli SET "safetystock:Milk & Cream" 2` |

### Viewing Configuration

| Command | Purpose | Example |
|---------|---------|---------|
| `redis-cli KEYS "safetystock:*"` | List all safety stock configs | Lists all keys starting with `safetystock:` |
| `redis-cli GET "safetystock:<category>"` | Get specific safety stock value | `redis-cli GET "safetystock:Milk & Cream"` |
| `redis-cli MGET $(redis-cli KEYS "safetystock:*")` | Get all values | Returns all safety stock configurations |

### Updating Configuration

| Command | Purpose | Example |
|---------|---------|---------|
| `redis-cli SET "safetystock:<category>" <new_value>` | Update existing value | `redis-cli SET "safetystock:Milk & Cream" 3` |
| `redis-cli DEL "safetystock:<category>"` | Delete config (falls back to next priority) | `redis-cli DEL "safetystock:Milk & Cream"` |

## Data Flow

```
Stock Update Message (MSK/Kafka)
    ↓
InventoryStockMessageProcessor.process()
    ├─ Extract available_quantity from payload
    ├─ Get previous_quantity from Redis
    └─ Call _update_typesense_if_needed()
        ├─ Fetch Typesense document
        ├─ Extract categories from document:
        │   ├─ collection_name → category
        │   ├─ parent_name → sub_category
        │   └─ category_name → sub_sub_category
        ├─ SafetyStockConfigManager.get_safety_stock()
        │   └─ Check Redis in priority order
        │   └─ Return resolved safety_stock
        ├─ Calculate typesense values:
        │   ├─ is_available = qty > safety_stock
        │   └─ available_qty = min(qty - safety_stock, MAX_DISPLAY_QUANTITY)
        └─ Update Typesense if values changed
```

## Implementation Details

### Files Created

| File | Purpose | Lines |
|------|---------|-------|
| `app/safety_stock_config.py` | SafetyStockConfigManager class with priority resolution | ~140 |

### Files Modified

| File | Changes | Lines |
|------|---------|-------|
| `app/inventory_stock_processor.py` | Initialize SafetyStockConfigManager, extract categories, resolve safety stock | ~60 |

### Methods

#### SafetyStockConfigManager.get_safety_stock()

| Parameter | Type | Description |
|-----------|------|-------------|
| `category` | Optional[str] | Category name from Typesense `collection_name` |
| `sub_category` | Optional[str] | Sub-category name from Typesense `parent_name` |
| `sub_sub_category` | Optional[str] | Sub-sub-category name from Typesense `category_name` |

| Return | Type | Description |
|--------|------|-------------|
| `safety_stock` | int | Resolved safety stock value (1-4 priority levels) |

## Performance

| Metric | Value |
|--------|-------|
| Typesense API calls per stock update | 1 (reused existing fetch) |
| Redis queries per stock update | 1 |
| Overall impact | Minimal |

## Logging

### Log Messages

| Log Level | Message | Meaning |
|-----------|---------|---------|
| INFO | `SAFETY_STOCK_CONFIG: Using sub_sub_category config - sub_sub_category='...', safety_stock=X` | Found config at priority 1 |
| INFO | `SAFETY_STOCK_CONFIG: Using sub_category config - sub_category='...', safety_stock=X` | Found config at priority 2 |
| INFO | `SAFETY_STOCK_CONFIG: Using category config - category='...', safety_stock=X` | Found config at priority 3 |
| INFO | `SAFETY_STOCK_CONFIG: No category-level config found, using environment default - safety_stock=X` | Using priority 4 (env default) |
| INFO | `INVENTORY_PROCESSOR: Extracted categories for FAC001:ROZ001 - category='...', sub_category='...', sub_sub_category='...'` | Categories extracted from Typesense |
| INFO | `INVENTORY_PROCESSOR: Resolved safety stock for FAC001:ROZ001 = X` | Safety stock resolved |
| INFO | `INVENTORY_PROCESSOR: New availability: True, new typesense quantity: 13 (safety_stock=2)` | Calculation result |

## Troubleshooting

### Issue: Safety Stock Not Applied

| Check | Command | Expected |
|-------|---------|----------|
| Config exists | `redis-cli GET "safetystock:Groceries"` | Returns integer value |
| Category names match | Check logs for "Extracted categories" | Exact case-sensitive match |
| Typesense has categories | Check Typesense document | `collection_name`, `parent_name`, `category_name` fields present |

### Issue: Wrong Safety Stock Value

| Check | Command | Expected |
|-------|---------|----------|
| Priority order | Check logs for "Using X config" | Most specific level should be used |
| Multiple configs | `redis-cli KEYS "safetystock:*"` | Verify which level has config |
| Redis values | `redis-cli GET "safetystock:*"` | Should be integers |

## Environment Variables

| Variable | Default | Purpose |
|----------|---------|---------|
| `SAFETY_QUANTITY` | 10 | Fallback safety stock (priority 4) |
| `MAX_DISPLAY_QUANTITY` | 20 | Maximum quantity to display in Typesense |

## Testing Scenarios

| Scenario | Setup | Expected Result |
|----------|-------|-----------------|
| No Redis config | No `safetystock:*` keys | Uses env SAFETY_QUANTITY (10) |
| Category-level config | `safetystock:Groceries = 4` | Uses 4 |
| Sub-category-level config | `safetystock:Dairy Products = 3` | Uses 3 |
| Sub-sub-category-level config | `safetystock:Milk & Cream = 2` | Uses 2 |
| Priority test | All three levels configured | Uses sub_sub_category value |
| Missing categories | Typesense doc missing fields | Falls back to env default |
| Null categories | Categories are None | Falls back to env default |

## Summary

| Aspect | Details |
|--------|---------|
| **Configuration** | Redis-based, no code changes needed |
| **Priority** | 4-level hierarchy (most specific wins) |
| **Performance** | Zero duplicate API calls, one Redis query per update |
| **Backward Compatible** | Yes, falls back to env default if no config |
| **Deployment** | Ready, no breaking changes |
| **Status** | ✅ Production Ready |
