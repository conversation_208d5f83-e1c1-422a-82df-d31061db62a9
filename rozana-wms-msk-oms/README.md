# Rozana WMS MSK OMS Lambda Function

This Lambda function consumes WMS order status update messages from Kaf<PERSON> and updates order statuses in the OMS service with facility-specific filtering. The function has been simplified to focus on payload extraction and processing.

## Overview

The function performs the following actions:

1. Consumes messages from MSK/Kafka containing WMS order status update messages
2. Processes the message to extract order ID, facility (warehouse), item SKUs, and their statuses
3. Maps WMS statuses to OMS statuses
4. Updates the order and item statuses directly in the OMS database with facility filtering

## Architecture

The Lambda function integrates with the existing OMS service, reusing its business logic for status updates rather than duplicating it. This ensures that the source of truth for order management remains the OMS service.

Components:

- **lambda_function.py**: Entry point that handles Kafka events and manages message processing
- **order_processor.py**: Processes WMS messages, maps fields, and coordinates updates
- **order_service.py**: Connects to OMS database to update statuses with facility filtering
- **constants.py**: Defines constants including order statuses and their mappings

### Key Features

- **Facility-Based Filtering**: Orders are filtered by facility/warehouse name to ensure updates only affect orders from the correct store
- **Support for MSK/Kafka Format**: Handles MSK Kafka event format
- **Configurable Payload Decoding**: Toggle between decoded and raw payloads for testing and debugging
- **Direct OMS Database Integration**: Updates order and item statuses directly in OMS database

## Environment Variables

The following environment variables need to be configured:

```env
# Database Connection (single URL format)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/oms_db

# Payload Decoding Control
PAYLOAD_DECODE=true           # Set to 'false' for local testing to skip base64 decoding

# Logging
LOG_LEVEL=INFO                # Options: DEBUG, INFO, WARNING, ERROR
```

## Local Development

To run the Lambda function locally:

1. Create a `.env` file from the provided `.sample.env`
2. Set `PAYLOAD_DECODE=false` for local testing to skip base64 decoding
3. Run `docker-compose up --build`
4. Test the Lambda using the AWS Lambda Runtime Interface Emulator:

```bash
curl "http://localhost:9000/2015-03-31/functions/function/invocations" -d '{
  "records": {
    "oms-updates": [
      {
        "value": "<json-string-or-base64-encoded-json-string>",
        "headers": [
          {"warehouse_name": [84, 69, 83, 84, 95, 87, 72, 49]},
          {"idempotent_key": [49, 50, 51, 52, 53]}
        ]
      }
    ]
  }
}'
```

## Deployment

To deploy to AWS Lambda:

1. Build the Docker image: `docker build -t rozana-wms-msk-oms .`
2. Tag and push to your ECR repository
3. Create a Lambda function using the container image
4. Configure environment variables
5. Set up MSK trigger with the appropriate topic

## Field Mapping

The Lambda maps the following fields from WMS to OMS:

- `warehouse_name` (from headers) → `facility_name` (critical for facility filtering)
- `order_reference` → `order_id`
- `sku_code` → `sku`
- `status` → Mapped to OMS status values using constants.OrderStatus

### Status Mappings

WMS statuses are mapped to OMS statuses using the `ORDER_STATUSES` mapping defined in the constants module. This provides a centralized place to manage all status mappings between systems.

## Best Practices

1. **Facility Filtering**: Always include the correct `warehouse_name` in message headers for proper facility filtering
2. **Unique Message IDs**: Provide unique message IDs in Kafka headers (`idempotent_key`) for message tracking
3. **Database Connection**: Use the DATABASE_URL environment variable for database connection settings
4. **Local Testing**: Set `PAYLOAD_DECODE=false` for local testing to skip base64 decoding of payloads

## Monitoring and Maintenance

The Lambda provides logging for monitoring:

- Error tracking with specific error messages in CloudWatch logs
- Processing information logged at INFO level
- Success/failure response in the Lambda return value

The simplified implementation focuses on core functionality without additional monitoring overhead.
