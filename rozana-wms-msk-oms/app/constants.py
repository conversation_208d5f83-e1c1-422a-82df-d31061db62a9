"""
Constants for Order Management System and Warehouse Management System integration
"""

class OrderStatus:
    """
    Order status constants for lifecycle management across systems
    
    Numerical codes for system integration, string values for database storage
    """
    
    # Rozana (OMS) statuses - numerical codes
    OPEN = 10
    FULFILLED = 11
    PARTIALLY_FULFILLED = 12
    UNFULFILLED = 13
    
    # WMS statuses - numerical codes for integration
    WMS_SYNCED = 21
    WMS_SYNC_FAILED = 22
    
    # WMS processing statuses - numerical codes
    WMS_OPEN = 23
    WMS_INPROGRESS = 24
    WMS_PICKED = 25
    WMS_FULFILLED = 26
    WMS_INVOICED = 27
    
    # TMS statuses - numerical codes for integration
    TMS_SYNCED = 31
    TMS_SYNC_FAILED = 32
    DELIVERED = 35
    
    
    # String representation of statuses used in database
    DB_STATUS_MAP = {
        # OMS statuses
        "oms_open": OPEN,
        "oms_fulfilled": FULFILLED,
        "oms_partial_fulfilled": PARTIALLY_FULFILLED,
        "oms_unfulfilled": UNFULFILLED,

        # WMS statuses - string to code mapping
        "wms_synced": WMS_SYNCED,
        "wms_sync_failed": WMS_SYNC_FAILED,
        "open" : <PERSON><PERSON>_<PERSON>P<PERSON>,
        "in_progress": WMS_INPROGRESS,
        "picked": WMS_PICKED,
        "fulfilled": WMS_FULFILLED,
        "invoiced": WMS_INVOICED,

        # TMS statuses
        "tms_synced": TMS_SYNCED,
        "tms_sync_failed": TMS_SYNC_FAILED,
    }
    
    # Reverse mapping: code to string for database operations
    CODE_TO_DB_STATUS = {v: k for k, v in DB_STATUS_MAP.items()}
    
    @classmethod
    def get_status_name(cls, status_code: int) -> str:
        """Get human-readable status name from status code"""
        status_map = {
            cls.OPEN: "Open",
            cls.FULFILLED: "Fulfilled",
            cls.PARTIALLY_FULFILLED: "Partially Fulfilled",
            cls.UNFULFILLED: "Unfulfilled",
            cls.WMS_SYNCED: "WMS Synced",
            cls.WMS_SYNC_FAILED: "WMS Sync Failed",
            cls.WMS_PICKED: "Picked",
            cls.TMS_SYNCED: "TMS Synced",
            cls.TMS_SYNC_FAILED: "TMS Sync Failed",

        }
        return status_map.get(status_code, f"Unknown Status ({status_code})")
    
    @classmethod
    def is_rozana_status(cls, status_code: int) -> bool:
        """Check if status code is a Rozana (OMS) status"""
        return status_code in [cls.OPEN, cls.FULFILLED, cls.PARTIALLY_FULFILLED, cls.UNFULFILLED]
    
    @classmethod
    def is_wms_status(cls, status_code: int) -> bool:
        """Check if status code is a WMS status"""
        return status_code in [
            cls.WMS_SYNCED, cls.WMS_SYNC_FAILED, cls.WMS_OPEN, cls.WMS_PICKED, cls.WMS_INPROGRESS, cls.WMS_FULFILLED, cls.WMS_INVOICED
        ]
    
    @classmethod
    def is_tms_status(cls, status_code: int) -> bool:
        """Check if status code is a TMS status"""
        return status_code in [
            cls.TMS_SYNCED, cls.TMS_SYNC_FAILED,
        ]
