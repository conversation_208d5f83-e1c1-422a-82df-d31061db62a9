import os
import logging
import psycopg2
from typing import Dict
from app.constants import OrderStatus

# Configure logging
logger = logging.getLogger(__name__)

ORDER_STATUSES = OrderStatus.DB_STATUS_MAP


class OrderService:
    """
    Wrapper for OMS service functionality
    
    This class provides direct access to OMS database operations
    for updating order statuses rather than duplicating the logic.
    """

    def __init__(self):
        """Initialize the OMS wrapper with database connection URI"""
        self.db_uri = os.environ.get('DATABASE_URL', 'postgresql://postgres:postgres@localhost:5432/oms_db')
        self._db_conn = None

    def _get_db_connection(self):
        """Get a database connection (creating one if needed)"""
        if not self._db_conn or self._db_conn.closed:
            try:
                self._db_conn = psycopg2.connect(self.db_uri)
                logger.info("Connected to OMS database")
            except Exception as e:
                logger.error(f"Failed to connect to OMS database: {str(e)}")
                raise
        return self._db_conn

    def get_order_status(self, order_id: str, facility_name: str) -> str:
        """
        Get the status of an order in OMS
        
        Args:
            order_id: The order ID to query
            facility_name: The facility/warehouse name for filtering (optional)
            
        Returns:
            Optional[str]: The current status of the order, or None if not found
        """
        try:
            conn = self._get_db_connection()
            with conn.cursor() as cursor:
                query = "SELECT status FROM orders WHERE order_id = %s AND facility_name = %s"
                cursor.execute(query, (order_id, facility_name))
                result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Failed to get order status {order_id}: {str(e)}")
            return None

    def get_order_mode(self, order_id: str, facility_name: str):
        """Get the order_mode (e.g., 'app', 'pos', 'web') for an order"""
        try:
            conn = self._get_db_connection()
            with conn.cursor() as cursor:
                query = "SELECT order_mode FROM orders WHERE order_id = %s AND facility_name = %s"
                cursor.execute(query, (order_id, facility_name))
                result = cursor.fetchone()
            return result[0] if result else None
        except Exception as e:
            logger.error(f"Failed to get order mode {order_id}: {str(e)}")
            return None

    def update_order_status(self, order_id: str, status: str, facility_name: str) -> Dict:
        """
        Update the status of an order in OMS
        
        Args:
            order_id: The order ID to update
            status: The new status
            facility_name: The facility/warehouse name for filtering (optional)
            
        Returns:
            Dict: Result with success and message
        """
        conn = None
        try:
            # Normalize status to lowercase
            new_status = status

            conn = self._get_db_connection()
            with conn.cursor() as cursor:
                # First check if order exists
                query = "SELECT id FROM orders WHERE order_id = %s AND facility_name = %s"
                params = [order_id, facility_name]
                cursor.execute(query, params)
                if not cursor.fetchone():
                    logger.error(f"Order {order_id} not found in facility {facility_name}")
                    return {
                            "success": False,
                            "message": f"Order not found in facility {facility_name}"
                        }

                # Update order status
                update_query = """
                    UPDATE orders
                    SET status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s AND facility_name = %s
                """
                update_params = [new_status, order_id, facility_name]
                cursor.execute(update_query, update_params)
                rows_affected = cursor.rowcount
                conn.commit()
                logger.info(f"Order {order_id} status updated to {new_status} (rows affected: {rows_affected})")

                return {
                    "success": True,
                    "message": f"Order status updated to {new_status}",
                    "order_id": order_id
                }

        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Failed to update order status {order_id}: {str(e)}")
            return {
                "success": False,
                "message": f"Error updating order status: {str(e)}"
            }
            
    def update_item_status(self, order_id: str, sku: str, status: str, facility_name: str) -> Dict:
        """
        Update status of a specific item within an order
        
        Args:
            order_id: The order ID
            sku: The SKU of the item to update
            status: The new status
            facility_name: The facility/warehouse name for filtering
            
        Returns:
            Dict: Result with success and message
        """
        conn = None
        try:
            # Normalize status to lowercase
            new_status = status

            conn = self._get_db_connection()
            with conn.cursor() as cursor:
                # First check if order exists with facility filter
                order_query = "SELECT id FROM orders WHERE order_id = %s AND facility_name = %s"
                order_params = [order_id, facility_name]
                cursor.execute(order_query, order_params)
                if not cursor.fetchone():
                    logger.error(f"Order {order_id} not found in facility {facility_name}")
                    return {
                        "success": False,
                        "message": f"Order not found in facility {facility_name}"
                    }

                # Check if item exists in the order
                cursor.execute("""
                    SELECT oi.id FROM order_items oi
                    JOIN orders o ON oi.order_id = o.id
                    WHERE o.order_id = %s AND oi.sku = %s AND o.facility_name = %s
                """, (order_id, sku, facility_name))

                if not cursor.fetchone():
                    logger.error(f"Item with SKU '{sku}' not found in order '{order_id}'")
                    return {
                        "success": False,
                        "message": f"Item with SKU '{sku}' not found in order '{order_id}'"
                    }

                # Update item status
                cursor.execute("""
                        UPDATE order_items
                        SET status = %s, updated_at = CURRENT_TIMESTAMP
                        FROM orders o
                        WHERE order_items.order_id = o.id AND o.order_id = %s AND order_items.sku = %s AND o.facility_name = %s
                    """, (new_status, order_id, sku, facility_name))

                conn.commit()
                logger.info(f"Item with SKU '{sku}' in order '{order_id}' updated to status '{new_status}'")

                return {
                    "success": True,
                    "message": f"Item status updated to {new_status}",
                    "order_id": order_id,
                    "sku": sku
                }

        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Failed to update item status {order_id}/{sku}: {str(e)}")
            return {
                "success": False,
                "message": f"Error updating item status: {str(e)}"
            }

    def update_item_fulfilled_quantity(self, order_id: str, sku: str, fulfilled_quantity: float, facility_name: str) -> Dict:
        """
        Update fulfilled_quantity of a specific item within an order

        Args:
            order_id: The order ID
            sku: The SKU of the item to update
            fulfilled_quantity: The picked/fulfilled quantity from WMS (decimal/float)
            facility_name: The facility/warehouse name for filtering

        Returns:
            Dict: Result with success and message
        """
        conn = None
        try:
            conn = self._get_db_connection()
            with conn.cursor() as cursor:
                # First check if order exists with facility filter
                order_query = "SELECT id FROM orders WHERE order_id = %s AND facility_name = %s"
                order_params = [order_id, facility_name]
                cursor.execute(order_query, order_params)
                if not cursor.fetchone():
                    logger.error(f"Order {order_id} not found in facility {facility_name}")
                    return {
                        "success": False,
                        "message": f"Order not found in facility {facility_name}"
                    }

                # Check if item exists in the order
                cursor.execute("""
                    SELECT oi.id FROM order_items oi
                    JOIN orders o ON oi.order_id = o.id
                    WHERE o.order_id = %s AND oi.sku = %s AND o.facility_name = %s
                """, (order_id, sku, facility_name))

                if not cursor.fetchone():
                    logger.error(f"Item with SKU '{sku}' not found in order '{order_id}'")
                    return {
                        "success": False,
                        "message": f"Item with SKU '{sku}' not found in order '{order_id}'"
                    }

                # Update item fulfilled_quantity
                cursor.execute("""
                        UPDATE order_items
                        SET fulfilled_quantity = %s, updated_at = CURRENT_TIMESTAMP
                        FROM orders o
                        WHERE order_items.order_id = o.id AND o.order_id = %s AND order_items.sku = %s AND o.facility_name = %s
                    """, (fulfilled_quantity, order_id, sku, facility_name))

                conn.commit()
                logger.info(f"Item with SKU '{sku}' in order '{order_id}' fulfilled_quantity updated to {fulfilled_quantity}")

                return {
                    "success": True,
                    "message": f"Item fulfilled_quantity updated to {fulfilled_quantity}",
                    "order_id": order_id,
                    "sku": sku,
                    "fulfilled_quantity": fulfilled_quantity
                }

        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Failed to update item fulfilled_quantity {order_id}/{sku}: {str(e)}")
            return {
                "success": False,
                "message": f"Error updating item fulfilled_quantity: {str(e)}"
            }


    def close(self):
        """Close the database connection if it's open"""
        if self._db_conn and not self._db_conn.closed:
            self._db_conn.close()
            self._db_conn = None
            logger.info("Database connection closed")
            
    def __del__(self):
        """Destructor to ensure connection is closed"""
        self.close()
