import json
import base64
import logging
import traceback
import os
from app.order_processor import OrderMessageProcessor
from app.order_service import OrderService

# Environment variable to control decoding behavior
PAYLOAD_DECODE = os.environ.get('PAYLOAD_DECODE', 'true').lower() == 'true'

# Configure logging based on environment
log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
logger = logging.getLogger()
logger.setLevel(getattr(logging, log_level))

# Initialize components - lazy loading to avoid connection during cold start
oms_wrapper = None
processor = None

def get_processor():
    """Get or initialize the order processor"""
    global oms_wrapper, processor
    if processor is None:
        logger.info("Initializing OMS wrapper and order processor")
        oms_wrapper = OrderService()
        processor = OrderMessageProcessor(oms_wrapper)
    return processor

def lambda_handler(event, context):
    """
    Process Kafka messages and update OMS order statuses

    Event structure from MSK/Kafka:
    {
        "records": {
            "topic-name": [
                {
                    "value": "base64-encoded-json-string",
                    "headers": [
                        {"warehouse_name": [byte values]},
                        {"timestamp": [byte values]},
                        {"idempotent_key": [byte values]}
                    ]
                }
            ]
        }
    }

    The headers will be decoded to:
    {
        "warehouse_name": "TEST_WH1",
        "timestamp": "2025-06-23T02:20:55.747056+00:00",
        "idempotent_key": "60821870-4b73-4c0c-9e44-f0f2825d3b2d"
    }
    """
    logger.info(f"Received event: {json.dumps(event)}")


    try:
        # MSK/Kafka events have a different structure - records is a dict with topic names as keys
        records_dict = event.get('records', {})
        if not records_dict:
            logger.error("No records found in event")
            return {"statusCode": 200, "body": json.dumps({"error": "No records found"})}
        
        # Process each topic
        for topic_name, records in records_dict.items():
            logger.info(f"Processing topic: {topic_name} with {len(records)} records")

            # Process each record in the topic
            for record in records:
                try:
                    logger.info(f"Processing record: {json.dumps(record)}")

                    # Validate record structure
                    if not isinstance(record, dict):
                        logger.error(f"Error: Record is not a dictionary, type={type(record).__name__}, value={record}")
                        continue

                    if 'value' not in record:
                        logger.error(f"Error: No 'value' key in record: {record}")
                        continue

                    payload = decode_kafka_record(record)
                    if not payload:
                        continue

                    headers = extract_kafka_headers(record)
                    if not headers:
                        continue

                    # Get required fields
                    warehouse = headers.get('warehouse_name')
                    message_id = headers.get('idempotent_key')

                    # Validate we have required fields
                    if not warehouse:
                        logger.error("No warehouse_name found in headers")
                        continue

                    # Process the record
                    processor = get_processor()
                    success = processor.process(warehouse, payload, message_id)
                    logger.info(f"processor response {success}")
                except Exception as e:
                    logger.error(f"Error processing Kafka record: {str(e)}")
                    logger.error(traceback.format_exc())
                    return {"statusCode": 500, "body": json.dumps({"error": str(e)})}


        return {"statusCode": 200, "body": json.dumps({"message": "Processing completed"})}
    except Exception as e:
        logger.error(f"Error processing Kafka record: {str(e)}")
        logger.error(traceback.format_exc())
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}


def decode_kafka_record(record):
    payload = None
    try:
        # If PAYLOAD_DECODE is false, return the raw value for testing purposes
        if not PAYLOAD_DECODE:
            logger.info("Skipping payload decode due to PAYLOAD_DECODE=false")
            return record.get('value')
            
        # Normal decoding path
        value_str = base64.b64decode(record['value']).decode('utf-8')
        payload = json.loads(value_str)
    except Exception as e:
        logger.error(f"Error decoding Kafka record: {str(e)}")

    logger.info(f"Decoded payload: {json.dumps(payload)}")
    return payload

def extract_kafka_headers(record):
    """Extract and decode Kafka headers"""
    headers = {}
    
    # If PAYLOAD_DECODE is false, return headers as-is for testing purposes
    if not PAYLOAD_DECODE:
        logger.info("Skipping headers extraction due to PAYLOAD_DECODE=false")
        # "headers": [
        #   {"warehouse_name": "TEST_WH1"},
        #   {"idempotent_key": "skjhshbssvhshsgss"}
        # ]
        headers = {key: value for d in record["headers"] for key, value in d.items()}
        return headers

    # Extract headers - MSK headers are a list of dicts with byte arrays
    if 'headers' not in record or not isinstance(record['headers'], list):
        logger.error(f"Error: No valid 'headers' list in record: {record}")
        return None
    
    for header_item in record['headers']:
        if not isinstance(header_item, dict):
            continue
        
        # Process each header key
        for key, value in header_item.items():
            if isinstance(value, list):
                # Convert byte array to string
                headers[key] = ''.join([chr(b) for b in value])

    logger.info(f"Extracted headers: {json.dumps(headers)}")
    return headers
