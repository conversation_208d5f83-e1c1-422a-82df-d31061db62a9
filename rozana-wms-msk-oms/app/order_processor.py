import json
import logging
from typing import Dict, Any, Tu<PERSON>, List
from app.order_service import OrderService
from app.constants import OrderStatus

ORDER_STATUSES = OrderStatus.DB_STATUS_MAP

# Configure logging
logger = logging.getLogger(__name__)

class OrderMessageProcessor:
    """
    Processes WMS order update messages and forwards them to OMS
    """
    
    def __init__(self, oms_service: OrderService):
        """
        Initialize the processor with an OMS service instance
        
        Args:
            oms_service: Instance of OrderService to communicate with OMS
        """
        self.oms_service = oms_service
        
    def process(self, warehouse: str, payload: Dict[str, Any], message_id: str = None) -> bool:
        """
        Process a WMS order update message
        
        Args:
            warehouse: The warehouse/facility name
            payload: The WMS message payload
            message_id: message ID for idempotency checks
            
        Returns:
            bool: True if the message was processed successfully
        """
        try:
            # Detect message format and extract order data
            order_data, order_id, order_status = self._extract_order_data(payload)
            if not all([order_data, order_id, order_status]):
                logger.error("Invalid order data", json.dumps(payload))
                return False

            logger.info(f"Processing order {order_id} with status {order_status} from warehouse {warehouse}")

            # warehouse -> facility name
            facility_name = warehouse
            
            # Check if order exists in OMS for this facility
            current_status = self.oms_service.get_order_status(order_id, facility_name)
            if current_status is None:
                logger.error(f"Order {order_id} not found in facility {facility_name}")
                return False
            
            # Process items if available
            items = order_data.get('items', [])
            processed_items = self._process_items(items)
                
            # Map order status from WMS to OMS status
            oms_order_status = self._map_status(order_status)
            
            # Fetch order_mode once and normalize
            order_mode = self.oms_service.get_order_mode(order_id, facility_name)
            order_mode_lower = (order_mode or '').lower()
            order_status_lower = str(order_status).strip().lower()

            # Special rule (minimal): POS + invoiced => Delivered (35)
            if order_status_lower in ('invoiced', 'customer_invoices') and order_mode_lower == 'pos':
                oms_order_status = OrderStatus.DELIVERED
                logger.info(f"POS order {order_id} invoiced -> setting status to DELIVERED (35)")
            
            # Update order status in OMS with facility filtering
            result = self.oms_service.update_order_status(
                order_id=order_id,
                status=oms_order_status,
                facility_name=facility_name
            )
            
            if not result.get('success'):
                logger.error(f"Failed to update order status in OMS: {order_id}, reason: {result.get('message')}")
                return False
                
            # Update item statuses
            item_update_success = self._update_items(order_id, facility_name, processed_items)
            
            # Minimal direct check again: POS + invoiced => set each provided item's status to 35 using existing function
            if order_status_lower in ('invoiced', 'customer_invoices') and order_mode_lower == 'pos':
                failures = []
                for it in items:
                    sku = it.get('sku_code') or it.get('sku')
                    if not sku:
                        continue
                    res = self.oms_service.update_item_status(order_id, sku, OrderStatus.DELIVERED, facility_name)
                    if not res.get('success'):
                        failures.append(sku)
                if failures:
                    logger.warning(f"Failed to set DELIVERED for some items in POS order {order_id}: {failures}")
                else:
                    logger.info(f"Updated provided items to DELIVERED (35) for POS order {order_id}")
            
            if not item_update_success:
                logger.error(f"Failed to update item statuses in OMS: {order_id}")
                return False

            logger.info(f"Successfully processed order {order_id} update to status {oms_order_status} in facility {facility_name}")
            return True
        except Exception as e:
            logger.error(f"Error processing WMS message: {str(e)}", exc_info=True)
            return False
            
    def _extract_order_data(self, payload: Dict[str, Any]) -> Tuple[Dict, str, str]:
        """
        Extract order data, ID and status from different possible message formats
        
        Args:
            payload: The message payload
            
        Returns:
            Tuple of (order_data, order_id, order_status)
        """
        # Format 1: WMS standard format with data field
        if 'data' in payload:
            data = payload['data']
            
            # Try to get order_reference first, then order_id
            order_id = data.get('order_reference')
            if not order_id:
                logger.error(f"Missing order_id/order_reference in data: {data}")
                return None, None, None
                
            order_status = data.get('order_status')
            if not order_status:
                logger.error(f"Missing order_status in data: {data}")
                return None, None, None
                
            return data, order_id, order_status


    def _process_items(self, items: List[Dict]) -> List[Dict]:
        """
        Process order items and extract required information
        
        Args:
            items: List of item data
            
        Returns:
            List of processed items with mapped fields
        """
        processed_items = []

        if not items:
            logger.error("No items found in payload")
            return processed_items

        for item in items:
            # sku_code -> sku
            sku = item.get('sku_code')
            if not sku:
                logger.warning(f"Item missing sku/sku_code, skipping: {item}")
                continue

            # Extract item status
            item_status = item.get('status')
            if not item_status:
                logger.warning(f"Item missing status, skipping: {item}")
                continue

            # Extract picked_quantity (fulfilled quantity)
            picked_quantity = item.get('picked_quantity')
            if picked_quantity is not None:
                try:
                    picked_quantity = float(picked_quantity)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid picked_quantity '{picked_quantity}' for item {sku}, setting to 0")
                    picked_quantity = 0
            else:
                logger.warning(f"Item missing picked_quantity for SKU {sku}, setting to 0")
                picked_quantity = 0

            # Create processed item with basic fields
            processed_item = {
                'sku': sku,
                'status': item_status,
                'picked_quantity': picked_quantity
            }

            processed_items.append(processed_item)

        return processed_items
        
    def _update_items(self, order_id: str, facility_name: str, items: List[Dict]) -> bool:
        """
        Update status and fulfilled quantity of multiple items
        
        Args:
            order_id: The order ID
            facility_name: The facility name for filtering
            items: List of processed items
            
        Returns:
            bool: True if all critical updates succeeded
        """
        all_critical_succeeded = True
        
        for item in items:

            # Map WMS status to OMS status
            mapped_status = self._map_status(item['status'])

            # Update item status in OMS
            result = self.oms_service.update_item_status(
                order_id=order_id,
                sku=item['sku'],
                status=mapped_status,
                facility_name=facility_name
            )
            if not result.get('success'):
                all_critical_succeeded = False
                logger.warning(f"Failed to update item status in OMS: {order_id}/{item['sku']}, reason: {result.get('message')}")

            # Update fulfilled quantity in OMS if picked_quantity is available
            if 'picked_quantity' in item:
                fulfilled_result = self.oms_service.update_item_fulfilled_quantity(
                    order_id=order_id,
                    sku=item['sku'],
                    fulfilled_quantity=item['picked_quantity'],
                    facility_name=facility_name
                )
                if not fulfilled_result.get('success'):
                    all_critical_succeeded = False
                    logger.warning(f"Failed to update item fulfilled_quantity in OMS: {order_id}/{item['sku']}, reason: {fulfilled_result.get('message')}")
                else:
                    logger.info(f"Successfully updated fulfilled_quantity to {item['picked_quantity']} for item {item['sku']} in order {order_id}")

        return all_critical_succeeded
            
    def _map_status(self, wms_status: str) -> str:
        """
        Map WMS status to OMS status
        
        Args:
            wms_status: Status from WMS
            
        Returns:
            str: Corresponding OMS status
        """
        # Map of WMS statuses to OMS statuses
        # Add more mappings as needed
        status_map = ORDER_STATUSES
        mapped_status = status_map.get(wms_status)
        if mapped_status is None:
            logger.warning(f"Unknown WMS status: {wms_status}")
            raise ValueError(f"Unknown WMS status: {wms_status}")

        logger.info(f"Mapped WMS status {wms_status} to OMS status {mapped_status}")
        return mapped_status
